{"expo": {"name": "imvisentencesreadtest", "slug": "imvisentencesreadtest", "version": "1.0.0", "orientation": "landscape", "icon": "./assets/imvi-logo.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-localization"]}}