const createExpoWebpackConfigAsync = require('@expo/webpack-config');

module.exports = async function (env, argv) {
  const config = await createExpoWebpackConfigAsync(
    {
      ...env,
      babel: {
        dangerouslyAddModulePathsToTranspile: [
          '@gorhom/bottom-sheet',
        ],
      },
      resolve: {
        alias: {
          'react-native$': 'react-native-web',
          'react-native/Libraries/Image/AssetRegistry$':
      'react-native-web/dist/modules/AssetRegistry',
        }
      },
    },
    argv
  );
  // config.resolve.alias['react-native'] = 'react-native-web';
  return config;
};