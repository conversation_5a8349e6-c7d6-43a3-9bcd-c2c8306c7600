import * as React from "react";
import { useEffect } from "react";
import AppNavigator from "./src/navigation/AppNavigator";
import { store } from "./src/store/store";
import { Provider } from "react-redux";
import { SafeAreaProvider } from "react-native-safe-area-context";
import {Platform} from 'react-native';


 // issue in reanimated when run on web
 window._frameTimestamp = null


function App() {
  return <AppNavigator />;
}


export default () => {
  return (
    <SafeAreaProvider style={{ flex: 1 }}>
      <Provider store={store}>
        <App />
      </Provider>
    </SafeAreaProvider>
  );
};

