{"name": "imvisentencesreadtest", "homepage": "https://tests.imvilabs.com/v2/sc", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@react-navigation/bottom-tabs": "^6.5.0", "@react-navigation/elements": "^1.3.15", "@react-navigation/native": "^6.0.16", "@react-navigation/native-stack": "^6.9.4", "@reduxjs/toolkit": "^1.9.1", "@shopify/flash-list": "^1.6.3", "expo": "^49.0.21", "expo-dev-client": "~2.4.12", "expo-font": "~11.4.0", "expo-linking": "~5.0.2", "expo-localization": "~14.3.0", "expo-screen-orientation": "~6.0.6", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "firebase": "^9.14.0", "i18next": "^23.2.11", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^13.0.2", "react-native": "0.72.6", "react-native-mmkv": "^2.11.0", "react-native-responsive-fontsize": "^0.5.1", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-uuid": "^2.0.1", "react-native-walkthrough-tooltip": "^1.5.0", "react-native-web": "~0.19.6", "react-redux": "^8.0.5", "resize-observer-polyfill": "^1.5.1", "styled-components": "^5.3.10"}, "resolutions": {"styled-components": "^5"}, "devDependencies": {"@babel/core": "^7.19.3", "@expo/webpack-config": "^19.0.0", "easytimer-react-hook": "^2.1.0", "easytimer.js": "^4.6.0", "expo-cli": "^6.3.10"}, "private": true}