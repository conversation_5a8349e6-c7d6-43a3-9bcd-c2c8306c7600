// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
// import { getAnalytics } from "firebase/analytics";
import { initializeFirestore } from "firebase/firestore";
import { getAuth, GoogleAuthProvider } from "firebase/auth";

// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional

const firebaseConfig_readChain = {
  apiKey: "AIzaSyC8k35mdpJA_4AnnppOdiZOi6mHb0kclmc",
  authDomain: "readchain-1e975.firebaseapp.com",
  databaseURL: "https://readchain-1e975-default-rtdb.europe-west1.firebasedatabase.app",
  projectId: "readchain-1e975",
  storageBucket: "readchain-1e975.appspot.com",
  messagingSenderId: "921308109737",
  appId: "1:921308109737:web:aa60634baf626182f782b8",
  measurementId: "G-GX3D58TLGR"
};

const app_readChain = initializeApp(firebaseConfig_readChain);
// Initialize Firebase
// const app = initializeApp(firebaseConfig, "secondary");

export const db = initializeFirestore(app_readChain, {
  experimentalForceLongPolling: true,
});
// export const auth = getAuth(app_readChain);


// const analytics = getAnalytics(app);

export const provider = new GoogleAuthProvider();