import React from "react";
import { Text, View } from "react-native";
import { NavigationContainer, DefaultTheme } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import MainScreen from "../screens/MainScreen";
import { useSelector } from "react-redux";
import ResultScreen from "../screens/ResultScreen";
import InstructionsScreen from "../screens/InstructionsScreen";
import PracticeScreen from "../screens/PracticeScreen";
import * as Linking from 'expo-linking';
import Header from "../components/Header";
import { useTranslation } from 'react-i18next';
import HomeScreen from "../screens/HomeScreen";
import AboutScreen from "../screens/AboutScreen";


const Stack = createNativeStackNavigator();

const ScreensTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: 'rgb(249, 249, 249)',
  },
};

const AppNavigator = () => {
  const { t } = useTranslation();
  const lang = useSelector((state) => state.lang.value);


  const config = {
    screens: {
      Home: {
        initialRouteName: '',
        path: 'v2/sc/home/<USER>/:lettersTestId?/:userId?/:lang?/:standalone?' 
      },
      Instructions: {path: 'v2/sc/instructions'},
      About: {path: `v2/sc/about`},
      Main: {path: 'v2/sc/main'},
      Practice: {path: 'v2/sc/practice'},
      Result: {path: 'v2/sc/result'}
    },
  };
  
  const linking = {
    prefixes: [Linking.createURL('/'), 'https://*.imvilabs.com/v2/sc/*'],
    config,
  };
  

  return (
      <NavigationContainer linking={linking} theme={ScreensTheme}>
        <Stack.Navigator>
        <Stack.Screen
            name="Home"
            options={({route}) => {
             return { headerStyle: {
                backgroundColor: "#ffffff",
              },
              header: () => <Header />,
              headerLeft: ()=> null,
              headerShadowVisible: false
            }}}
            component={HomeScreen}
          />
          <Stack.Screen
            name="About"
            options={({route}) => {
             return { headerStyle: {
                backgroundColor: "#ffffff",
              },
              header: () => null,
              headerLeft: () => null,
              headerShadowVisible: false
            }}}
            component={AboutScreen}
          />
          <Stack.Screen
            name="Instructions"
            options={{
              headerStyle: {
                backgroundColor: "#ffffff",
              },
              header: () => ( <Header subTitle={'instructions'} />),
              headerShadowVisible: false
            }}
            component={InstructionsScreen}
          />
          <Stack.Screen
            name="Practice"
            options={{
              headerShown: false,
            }}
            component={PracticeScreen}
          />
          <Stack.Screen
            name="Main"
            options={{
              headerShown: false,
            }}
            component={MainScreen}
          />
          <Stack.Screen
            name="Result"
            component={ResultScreen}
            options={{
              headerStyle: {
                backgroundColor: "#F9F9F9",
              },
              headerTitle: () => (
                <View>
                <Text lang={lang} style={{ fontWeight: "700", fontSize: 24, paddingLeft: 8, paddingTop: 15, fontFamily: "Avenir", color: "#1C1C1C" }}>
                {t('results')}
                </Text>
                </View>
              ),
              headerShadowVisible: false,
              headerLeft: ()=> null,
            }}
          />
        </Stack.Navigator>
      </NavigationContainer>
  );
};

export default AppNavigator;
