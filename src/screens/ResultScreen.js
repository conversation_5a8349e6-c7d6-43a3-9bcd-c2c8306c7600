import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  Platform,
  Pressable
} from "react-native";
import {useDispatch, useSelector } from "react-redux";
import {
  getDoc,
  doc,
} from "firebase/firestore";
import { db } from "../../firebaseConfig";
import { useTranslation } from 'react-i18next';
import { setLang } from "../store/langSlice";
import * as Linking from 'expo-linking';
import { shadow } from "../components/Shadow";


const ResultScreen = () => {
  const standalone = useSelector((state) => state.testType.value);
  const lettersTestIdFromState = useSelector((state) => state.score.lettersTestId);
  const userIdFromState = useSelector((state) => state.score.userId);
  const wordsTestIdFromState = useSelector((state) => state.score.wordsTestId);
  const testId = useSelector((state) => state.answer.testId);
  const [scroeFromDB, setScroeFromDB] = useState({});
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();

  useEffect(() => {
    getScoreFromDb();
  }, []);

  const getScoreFromDb = async () => {
    let letDoRef= null;
    let worDoRef= null;
    let letDocSnap = null;
    let worDocSnap = null;
      const senDoRef = doc(
        db,
        `UsersTests/${userIdFromState}/Tests`,
        testId
      );
      if(standalone === "false"){
         letDoRef = doc(
          db,
          `UsersTests/${userIdFromState}/Tests`,
          lettersTestIdFromState
        );
        worDoRef = doc(
          db,
          `UsersTests/${userIdFromState}/Tests`,
          wordsTestIdFromState
        );
      }
      
      const senDocSnap = await getDoc(senDoRef);
      if(standalone === "false"){
       letDocSnap = await getDoc(letDoRef);
       worDocSnap = await getDoc(worDoRef);
      }
      dispatch(setLang(senDocSnap.data().language))
      i18n.changeLanguage(senDocSnap.data().language)
      if(standalone === "false"){
      setScroeFromDB({
        sentencesScore: senDocSnap.data().score,
        lettersScore: letDocSnap.data().score,
        wordsScore: worDocSnap.data().score,
      });
    }else{
      setScroeFromDB({
        sentencesScore: senDocSnap.data().score,
      });
    }
    
  };

  const closeApp = async () => {
    const url = "https://tests.imvilabs.com/toimviapp"
    if(Platform.OS == 'web'){
      window.open(url, '_self');
   }else{
    await Linking.openURL(url).catch((err) => console.error('An error occurred', err));
   }
  }

  return (
    <View style={styles.container}>
      <View style={styles.resultItem}>
        <View style={[styles.results, styles.dishShadow]}>
          {standalone === "false" && <View style={styles.resGroup}>
            <Text style={styles.resultNum}>{scroeFromDB.lettersScore}</Text>
            <Text style={styles.resultItemText}>{t('letters')}</Text>
          </View>}
          {standalone === "false" && <View style={styles.resGroup}>
            <Text style={styles.resultNum}>{scroeFromDB.wordsScore}</Text>
            <Text style={styles.resultItemText}>{t('words')}</Text>
          </View>}
          <View style={styles.resGroup}>
            <Text style={styles.resultNum}>{scroeFromDB.sentencesScore}</Text>
            <Text style={styles.resultItemText}>{standalone === "false" ? t('sentences') : t('CorrectAnswers')}</Text>
          </View>
        </View>
         <Pressable
            onPress={closeApp}
            style={[
              styles.button,
              { backgroundColor: "rgb(70, 121, 188)", marginBottom: 20 }
            ]}
          >
            <Text style={styles.buttonText}>{t('continue')}</Text>
          </Pressable> 
      </View>
      
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
  },
  resultItem: {
    flex: 1,
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    height: "100%",
     marginTop: "25%",
  },
  resultItemText: {
    color: "#494949",
    marginTop: 10,
    fontWeight: 600,
  },
  button: {
    backgroundColor: "rgb(70, 121, 188)",
    width: "100%",
    padding: Platform.OS === "ios" ? 17 : 14,
    borderRadius: 10,
    alignItems: "center",
  },
  buttonContainer: {
    width: "90%",
    marginVertical: 25,
  },
  buttonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
  resGroup: {
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  results: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-around",
    paddingVertical: 40,
  },
  resultSections: {
    width: "100%",
    alignItems: "center"
  },
  inputContainer: {
    width: "100%",
    marginTop: 30,
  },
  input: {
    backgroundColor: "white",
    paddingHorizontal: 15,
    paddingVertical: Platform.OS === "ios" ? 12 : 10,
    borderRadius: 10,
    marginTop: 15,
    borderColor: "#d6d6d6",
    borderStyle: "solid",
    borderWidth: 1,
  },
  dengerBox: {
    padding: 5,
    backgroundColor: "rgb(255, 222, 222)",
  },
  dengerTxt: {
    color: "#EC3940",
  },
  infoWrap: {
    display: "flex",
    flex: 1,
    width: "90%",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    marginTop: "50%",
    marginBottom: 20,
  },
  info: {
    color: "#808080",
    fontSize: 18,
  },
  HeadInfo: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 20,
  },
  resultNum: {
    backgroundColor: "#E5EEFA",
    borderRadius: 22,
    paddingHorizontal: 25,
    paddingVertical: 15,
    fontSize: 25,
    fontWeight: "700",
  },
  dishShadow: {
    borderRadius: 10,
    ...shadow({
      elevation: 13,
      rgba: "rgba(0, 0, 0, 0.099)",
      opacity: 0.099,
      radius: 20,
      offsetWidth: 1,
      offsetHeight: 1
    })
  }
});

export default React.memo(ResultScreen);