import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import {
  View,
  StyleSheet,
  Text,
  Dimensions,
  Animated,
  Platform,
  ActivityIndicator,
  Image,
  Pressable,
} from "react-native";
import { storage } from "../mmkvStorage";
import { useFonts } from "expo-font";
import { useFetchSentencesQuery } from "../store/sentencesSlice";
import { FlashList } from "@shopify/flash-list";
import { useHeaderHeight } from "@react-navigation/elements";
import { setCorrectAnswer } from "../store/answerSlice";
import { useDispatch, useSelector } from "react-redux";
import Practice from "../components/Practice";
import * as ScreenOrientation from "expo-screen-orientation";
import { setOrientation } from "../store/orientationSlice";
import flip from "../../assets/flip.png";
import { useTranslation } from 'react-i18next';
import Separator from "../components/Separator";
import back from '../../assets/back.png'
import ExtraInstructionScreen from "./ExtraInstructionScreen";
import help from '../../assets/help.png';
import CustomBottomSheet from '../components/CustomBottomSheet';




const PracticeScreen = ({ route, navigation }) => {
  const { t } = useTranslation();
  const lang = useSelector((state) => state.lang.value);
  const orientation = useSelector((state) => state.orientation.value);
  const [skip, setSkip] = useState(true);
  const {
    data,
    isLoading,
    isError,
    error,
    isFetching,
    isUninitialized,
    refetch,
  } = useFetchSentencesQuery(lang, { skip });
  const [sentenceOfwords, setSentenceOfwords] = useState([]);
  const [clicks, setClicks] = useState([]);
  const headerHeight = useHeaderHeight();
  const [windowDims, setWindow] = useState(Dimensions.get("window"));
  const [sentenceIndex, setSentenceIndex] = useState(0);
  const [countCorrectSentences, setCountCorrectSentences] = useState([]);
  const [countWrongSentences, setCountWrongSentences] = useState([]);
  const [scoreRes, setScoreRes] = useState(false);
  const animatedValueRef = useRef(new Animated.Value(0)).current;
  const ref = useRef(null);
  const refElem = useRef(0);
  const [scrollDown, setScrollDown] = useState(false);
  const [firstClicks, setFirstClicks] = useState({});
  const [allowToMoveDown, setAllowToMoveDown] = useState(true);
  const dispatch = useDispatch();
  const [addedWrongElements, setAddedWrongElements] = useState([]);
  const [addedCorrectElements, setAddedCorrectElements] = useState([]);
  const [dataArr, setDataArr] = useState([]);
  const [practiceFinish, setPracticeFinish] = useState(false);
  const [extaSheetOpen, setExtaSheetOpen] = useState(false);
    const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);


  useEffect(() => {
    if(route.params.backToInstructions){
      setSkip(false)
    }
  }, []);
  

  useEffect(() => {
     resetLocalStorge()
    checkOrientation();
    const subscription = ScreenOrientation.addOrientationChangeListener(
      handleOrientationChange
    );
    return () => {
      ScreenOrientation.removeOrientationChangeListeners(subscription);
    };
  }, []);

  const checkOrientation = async () => {
    const orientation = await ScreenOrientation.getOrientationAsync();
    getScreenOrientation();
  };

  const handleOrientationChange = (o) => {
    getScreenOrientation();
  };
  

  useEffect(() => {
    arrengeSentences();
  }, [isLoading, skip]);

 

  useEffect(() => {
    countWrongSentences.forEach((s, i) => {
      if (!addedWrongElements.includes(s)) {
        setAddedWrongElements([...addedWrongElements, s]);
        setAddedCorrectElements((prev) =>
          prev.filter((sentence) => sentence !== s)
        );
      }
    });
    countCorrectSentences.forEach((s, i) => {
      if (!addedCorrectElements.includes(s)) {
        setAddedCorrectElements([...addedCorrectElements, s]);
        setAddedWrongElements((prev) =>
          prev.filter((sentence) => sentence !== s)
        );
      }
    });
  }, [countWrongSentences, countCorrectSentences, scoreRes]);

  const userAgentString = navigator.userAgent;
  let os15Agent = null;
  let safariAgent = null;
  if (userAgentString !== undefined) {
    os15Agent = userAgentString.indexOf("iPhone OS 15") > -1;
    safariAgent = userAgentString.indexOf("Chrome") > -1;
  }

  useEffect(() => {
    const handleChange = ({ screen, window: win }) => {
      setWindow(win);
    };

    const subscription = Dimensions.addEventListener("change", handleChange);
    return () => {
      subscription.remove();
    };
  }, [setWindow]);



  useEffect(() => {
    console.log("orientation", orientation);
    if (orientation === "portrait") {
      setIsBottomSheetVisible(true);
    } else {
      setSkip(false);
      setIsBottomSheetVisible(false);
    }
  }, [orientation]);
  

  const getScreenOrientation = () => {
    if (Math.abs(window.orientation) === 90) {
      dispatch(setOrientation("landscape"));
    } else {
      dispatch(setOrientation("portrait"));
    }
  };
  

  const arrengeSentences = () => {
    let practiceData = [];
    if (data !== undefined) {
      practiceData = data.slice(0, 4);
      setDataArr(practiceData);
    }
    practiceData.map((item) => {
      let arrayOfWords = item.sentence.split(" ");
      setSentenceOfwords((prev) => [
        ...prev,
        { id: item.id, arrayOfWords, correctPosition: item.correctPosition },
      ]);
    });
  };

  

  const onWordPressed = async (value) => {
    // If it hasn't been clicked, set its value in the "firstClicks" object to "true"
    const storedElement = storage.getString("letter_id");
    let clicksFromStorage = [];

    if (!firstClicks[value.sentenceKey]) {
      setFirstClicks({ ...firstClicks, [value.sentenceKey]: true });
    }

    if (storedElement === undefined) {
      clicksFromStorage.push(value);
    } else {
      clicksFromStorage = JSON.parse(storedElement);

      if (
        !clicksFromStorage.some(
          (e) =>
            e.sentenceKey == value.sentenceKey && e.wordKey == value.wordKey
        )
      ) {

        let testLines = [];
        const linesArrlength = getOccurrence(
          clicksFromStorage,
          value.sentenceKey
        );

        if (linesArrlength > 0) {
          clicksFromStorage.forEach((arrElm, indxElm) => {
            if (
              !(value.wordKey > arrElm.wordKey + 1) &&
              value.sentenceKey == arrElm.sentenceKey &&
              !(value.wordKey < arrElm.wordKey - 1) &&
              value.sentenceKey == arrElm.sentenceKey
            ) {
              testLines.push("true");
              clicksFromStorage.splice(
                clicksFromStorage.findIndex(
                  (e) =>
                    !(value.wordKey > e.wordKey + 1) &&
                    value.sentenceKey == e.sentenceKey &&
                    !(value.wordKey < e.wordKey - 1) &&
                    value.sentenceKey == e.sentenceKey
                ),
                1
              );
            }
          });
        }

        clicksFromStorage.push(value);
      } else if (
        clicksFromStorage.some(
          (e) =>
            e.wordKey === value.wordKey && e.sentenceKey === value.sentenceKey
        )
      ) {
        clicksFromStorage.splice(
          clicksFromStorage.findIndex(
            (e) =>
              e.wordKey === value.wordKey && e.sentenceKey === value.sentenceKey
          ),
          1
        );
      }

      const linesArrlength = getOccurrence(
        clicksFromStorage,
        value.sentenceKey
      );

      if (value.index == 0) {
        if (linesArrlength == 2 && allowToMoveDown) {
          setTimeout(() => {
            setScrollDown(true);
            ref.current?.scrollToIndex({
              index: value.index + 1,
              animated: true,
              viewOffset: windowDims.height - 20,
              viewPosition: 1,
            });
          }, 200);
          setAllowToMoveDown(false);
        }
      } else {
        if (linesArrlength == 2) {
           setTimeout(() => {
            setScrollDown(true);
            if(ref.current){
              ref.current?.scrollToIndex({
                index: value.index + 1,
                animated: true,
                viewPosition: 1,
              });
            }
           }, 200);
        }
      }
      openStarTestSheet(value, linesArrlength)
    }

    setClicks(clicksFromStorage);

    setSentenceIndex(value.index);

    storage.set("letter_id", JSON.stringify(clicksFromStorage));

    getScore(clicksFromStorage, value);
  };

  const goToTest = () => {
    navigation.replace("Main")
  };

  const snapPoints = useMemo(() => ["25%", "50%"], []);
  const snapPointsInstruction = useMemo(() => ["25%", "90%"], []);

  const handleSheetChanges = useCallback((index) => {
    },
    [orientation]
  );

  const onPressHelp = () => {
    setExtaSheetOpen(true)
    setIsBottomSheetVisible(true);
  }

  const closeHelpScreen = () => {
    setExtaSheetOpen(false)
    setIsBottomSheetVisible(false);
  }

  

  const openStarTestSheet = useCallback((value, linesArrlength) => {
      if (value.index === 3) {
        if (linesArrlength == 2) {
          setTimeout(() => {
            goToTest()
          }, 1000);
        }
      }
      
    },[])
  

  const getOccurrence = (array, value) => {
    return array.filter((v) => v.sentenceKey === value).length;
  };

  const getScore = async (arr, val) => {
    if (arr.length > 0) {
      const currentSentenceLines = arr.filter(
        (sentence, index) => sentence.sentenceKey === val.sentenceKey
      );
      let checkIfIsCorrectAnswer;
      let currentSentence;
      currentSentenceLines.forEach((sc, indx) => {
        currentSentence = dataArr.filter((v, index) => sc.sentenceKey === v.id);
      });

      let result = currentSentenceLines.map((a) => a.wordKey);

      result.sort((a, b) => {
        return a - b;
      });

      if (currentSentence !== undefined && currentSentence.length > 0) {
        checkIfIsCorrectAnswer = areEqual(
          result,
          currentSentence[0].correctPosition
        );
      }
      if (
        currentSentence !== undefined &&
        currentSentence.length > 0 &&
        !countWrongSentences.includes(currentSentence[0].sentence) &&
        currentSentenceLines.length > 2
      ) {
        setCountWrongSentences((prev) => [
          ...prev,
          currentSentence[0].sentence,
        ]);
      }
      if (
        currentSentenceLines.length == 1 &&
        firstClicks[val.sentenceKey] &&
        !countWrongSentences.includes(currentSentence[0].sentence)
      ) {
        setCountWrongSentences((prev) => [
          ...prev,
          currentSentence[0].sentence,
        ]);
      }

      if (currentSentenceLines.length == 1 || currentSentenceLines.length > 2) {
        setScoreRes(!scoreRes);
        setCountCorrectSentences((prev) =>
          prev.filter((sentence) => sentence !== currentSentence[0].sentence)
        );
      } else if (currentSentenceLines.length === 2) {
        if (countCorrectSentences.includes(currentSentence[0].sentence)) {
          if (checkIfIsCorrectAnswer) {
            setCountWrongSentences((prev) =>
              prev.filter(
                (sentence) => sentence !== currentSentence[0].sentence
              )
            );
            setCountCorrectSentences((prev) =>
              prev.filter(
                (sentence) => sentence !== currentSentence[0].sentence
              )
            );
            setCountCorrectSentences((prev) => [
              ...prev,
              currentSentence[0].sentence,
            ]);
          } else {
            if (!countWrongSentences.includes(currentSentence[0].sentence)) {
              setCountCorrectSentences((prev) =>
                prev.filter(
                  (sentence) => sentence !== currentSentence[0].sentence
                )
              );
              setCountWrongSentences((prev) => [
                ...prev,
                currentSentence[0].sentence,
              ]);
            }
          }
        } else {
          if (checkIfIsCorrectAnswer) {
            dispatch(setCorrectAnswer(true));
            setCountCorrectSentences((prev) => [
              ...prev,
              currentSentence[0].sentence,
            ]);
            setCountWrongSentences((prev) =>
              prev.filter(
                (sentence) => sentence !== currentSentence[0].sentence
              )
            );
          } else {
            dispatch(setCorrectAnswer(false));
            setCountCorrectSentences((prev) =>
              prev.filter(
                (sentence) => sentence !== currentSentence[0].sentence
              )
            );
            if (!countWrongSentences.includes(currentSentence[0].sentence)) {
              setCountWrongSentences((prev) => [
                ...prev,
                currentSentence[0].sentence,
              ]);
            }
            setScoreRes(!scoreRes);
          }
        }
      }
    }
  };

  function areEqual(array1, array2) {
    if (array1.length === array2.length) {
      return array1.every((element, index) => {
        if (element === array2[index]) {
          return true;
        }

        return false;
      });
    }

    return false;
  }
  

  const resetLocalStorge = async () => {
    storage.delete("letter_id");
  };

  const scroll_distance = windowDims.height;
  const renderItem = ({ item, index }) => {
    const animheight = animatedValueRef.interpolate({
      inputRange: [0, 20],
      outputRange: [scroll_distance, scroll_distance / 2],
      extrapolate: "clamp",
    });
    
    return (
      <Animated.View
        ref={refElem}
        style={[{
          width: windowDims.width,
          height: animheight,
          alignItems: "center",
          justifyContent: "center",
          paddingLeft: 30,
          paddingRight: 30,
          }
          ]}
      >
        <Practice
          sentence={item}
          index={index}
          sentenceKey={item.id}
          onWordPressed={onWordPressed}
          windowDims={windowDims}
          clicks={clicks}
          headerHeight={headerHeight}
          countWrongSentences={countWrongSentences}
          countCorrectSentences={countCorrectSentences}
        />
      </Animated.View>
    );
  };

  return (
    <View style={[styles.container]} id={"fallscreen_id"}>
    <Pressable hitSlop={30} 
        style={{position: "absolute" ,left: 20, top: 15, zIndex: 10}} 
      onPress={() => navigation.navigate('Instructions')}
      >
     <Image 
     source={back} 
     style={{width: 10, height: 18}}
     />
     </Pressable>
     <Pressable hitSlop={30} 
        style={{position: "absolute" ,right: 20, top: 15, zIndex: 10}} 
      onPress={onPressHelp}
      >
     <Image 
     source={help} 
     style={{width: 22, height: 22}}
     />
     </Pressable>
      {error ? (
        <Text>There was an error</Text>
      ) : isLoading || sentenceOfwords.length === 0 ? (
        <ActivityIndicator
          size="large"
          color="#0000ff"
          style={{ marginTop: (windowDims.height - headerHeight) / 2 }}
        />
      ) : (
        <FlashList
          data={sentenceOfwords}
          renderItem={renderItem}
          extraData={[clicks, countWrongSentences, countCorrectSentences]}
          estimatedItemSize={20}
          keyExtractor={(item) => item.id}
          // horizontal={true}
          ref={ref}
          scrollEnabled={scrollDown}
           initialScrollIndex={sentenceIndex}
          // showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { y: animatedValueRef } } }],
            { useNativeDriver: false }
          )}
            contentOffset={{ x: 0, y: scroll_distance }}
          viewabilityConfig={{
            waitForInteraction: true,
            itemVisiblePercentThreshold: 50,
            minimumViewTime: 50,
          }}
            //  disableAutoLayout={true}
          onViewableItemsChanged={() => setTimeout(() => {setScrollDown(false)}, 200)}
          ItemSeparatorComponent={() => <Separator />}
        />
      )}

      <CustomBottomSheet 
        isVisible={isBottomSheetVisible}
        height={extaSheetOpen ? '90%' : '50%'}
      >
        {orientation === "portrait" ? (
          <View style={styles.infoWrap}>
            <Image source={flip} style={{ width: 125, height: 125 }} />
            <Text style={styles.info}>
            {t("rotateYourPhone")}
            </Text>
          </View>
        ) : orientation === "landscape" && extaSheetOpen ? (
          <ExtraInstructionScreen closeHelpScreen={closeHelpScreen} />
        ) : null}
      </CustomBottomSheet>
     
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
  },
  header: {
    fontSize: 16,
    marginVertical: 10,
  },
  fadingContainer: {
    height: 19,
    with: 10,
    backgroundColor: "gray",
  },
  display: {
    flex: 1 / 24,
    justifyContent: "center",
    alignItems: "center",
  },
  displayText: {
    color: "#000",
    fontSize: 18,
    fontWeight: "200",
    fontFamily: Platform.OS === "ios" ? "Helvetica Neue" : null,
  },
  infoWrap: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
    marginBottom: 20,
  },
  info: {
    color: "#808080",
    paddingTop: 30,
    textAlign: "center",
  },
  button: {
    backgroundColor: "rgb(70, 121, 188)",
    width: "100%",
    padding: Platform.OS === "ios" ? 17 : 14,
    borderRadius: 10,
    alignItems: "center",
  },
  buttonContainer: {
    width: "50%",
    marginVertical: 25,
  },
  buttonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
  wrong: {
    fontFamily: "Avenir",
    fontSize: 16,
    color: "red",
  },
  correct: {
    fontFamily: "Avenir",
    fontSize: 16,
    color: "green",
  },
  iconContainer: {
    position: "absolute",
    top: 20,
    start: "40%",
    flexDirection: "row",
    paddingVertical: 4.5,
    paddingHorizontal: 6,
    borderWidth: 1,
    borderRadius: 2,
  },
});

export default React.memo(PracticeScreen);
