import { StyleSheet, Text, View, Pressable, Platform } from "react-native";
import React, { useEffect } from "react";
import { useFonts } from "expo-font";
import { useDispatch, useSelector } from "react-redux";
import Slider from "../components/Slider";
import * as ScreenOrientation from "expo-screen-orientation";
import { setOrientation } from "../store/orientationSlice";
import { useTranslation } from "react-i18next";
import { setLang } from "../store/langSlice";
import { getLocales } from "expo-localization";
import {
  setLettersTestId,
  setWordsTestId,
  setUserId,
} from "../store/scoreSlice";

const deviceLanguage = getLocales()[0].languageCode;

const InstructionsScreen = ({ route, navigation }) => {
  const { scrolledItem, index } = useSelector((state) => state.instructions);
  const lettersTestIdFromState = useSelector(
    (state) => state.score.lettersTestId
  );
  const userIdFromState = useSelector((state) => state.score.userId);
  const wordsTestIdFromState = useSelector((state) => state.score.wordsTestId);
  const langFromState = useSelector((state) => state.score.value);
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const { lettersTestId, wordsTestId, userId, lang } = route.params ?? {};

  const goFullScreen = () => {
    let docEl = window.document.documentElement;
    let elemFS = window.document.getElementById("fullscreen_id");

    // Trigger fullscreen
    if (elemFS.requestFullscreen) {
      docEl.requestFullscreen();
    } else if (elemFS.mozRequestFullScreen) {
      /* Firefox */
      docEl.mozRequestFullScreen();
    } else if (elemFS.webkitRequestFullscreen) {
      /* Chrome, Safari and Opera */
      docEl.webkitRequestFullscreen();
    } else if (elemFS.webkitRequestFullScreen) {
      /* Chrome, Safari and Opera */
      docEl.webkitRequestFullScreen();
    } else if (elemFS.msRequestFullscreen) {
      /* IE/Edge */
      docEl.msRequestFullscreen();
    }
  };

  useEffect(() => {
    if (lettersTestId && wordsTestId && userId && lang) {
      const userLang = lang.toLowerCase();
      i18n.changeLanguage(userLang);
      dispatch(setLang(userLang));
      dispatch(setLettersTestId(lettersTestId));
      dispatch(setWordsTestId(wordsTestId));
      dispatch(setUserId(userId));
    } else if (
      lettersTestIdFromState &&
      wordsTestIdFromState &&
      userIdFromState &&
      langFromState
    ) {
      i18n.changeLanguage(langFromState);
      dispatch(setLang(langFromState));
      dispatch(setLettersTestId(lettersTestIdFromState));
      dispatch(setWordsTestId(wordsTestIdFromState));
      dispatch(setUserId(userIdFromState));
    }
  }, []);

  useEffect(() => {
    checkOrientation();
    const subscription = ScreenOrientation.addOrientationChangeListener(
      handleOrientationChange
    );
    return () => {
      ScreenOrientation.removeOrientationChangeListeners(subscription);
    };
  }, []);

  const checkOrientation = async () => {
    const orientation = await ScreenOrientation.getOrientationAsync();
    getScreenOrientation();
  };

  const handleOrientationChange = (o) => {
    getScreenOrientation();
  };

  const getScreenOrientation = () => {
    if (Math.abs(window.orientation) === 90) {
      dispatch(setOrientation("landscape"));
    } else {
      dispatch(setOrientation("portrait"));
    }
  };

  const [fontsLoaded] = useFonts({
    Avenir: require("../../assets/fonts/Avenir-Regular.ttf"),
    Roboto: require("../../assets/fonts/Roboto-Regular.ttf"),
  });

  const isIPad = () => {
    const ua = navigator.userAgent;
    return (
      /Macintosh/i.test(ua) &&
      navigator.maxTouchPoints &&
      navigator.maxTouchPoints > 2
    );
  };

  const getIOSVersion = () => {
    const match = navigator.userAgent.match(/OS (\d+)_(\d+)_?(\d+)?/);
    return match
      ? [
          parseInt(match[1], 10),
          parseInt(match[2], 10),
          parseInt(match[3] || 0, 10),
        ]
      : null;
  };

  const navtoPractice = () => {
    if (typeof window !== "undefined" && window.navigator) {
      if (!isIPad()) {
        goFullScreen();
      }
    }
    navigation.replace("Practice", { backToInstructions: true });
  };

  const navtoTest = () => {
    goFullScreen();
    navigation.replace("Home");
  };

  if (!fontsLoaded) {
    return null;
  }

  return (
    <View style={[styles.wrapper]} id={"fullscreen_id"}>
      <Slider />
      <View
        style={{
          paddingHorizontal: 24,
          paddingBottom: 24,
          height: 80,
        }}
      >
        {index === 3 && (
          <View
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <Pressable
              style={[
                styles.pButton,
                {
                  backgroundColor: "#4679BC",
                },
              ]}
              onPress={navtoPractice}
            >
              <Text style={[styles.pButtonText, { color: "#FFFFFF" }]}>
                {t("practice")}
              </Text>
            </Pressable>
          </View>
        )}
      </View>
    </View>
  );
};

export default InstructionsScreen;

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    justifyContent: "space-between",
    backgroundColor: "#f9f9f9",
    paddingTop: 15,
  },
  lang_link: {
    paddingLeft: 5,
  },
  icon: {
    width: 64,
    height: 64,
  },
  letter: {
    fontFamily: "Roboto",
    fontFeatureSettings: "tnum",
    fontVariant: "tabular-nums common-ligatures",
    letterSpacing: 1.4,
    position: "relative",
  },
  infoText: {
    fontFamily: "Avenir",
    fontSize: 16,
    color: "#4E4E4E",
  },
  inst: {
    width: 311.37,
    height: 179.81,
  },
  instItems: {
    display: "flex",
    flexDirection: "row",
  },
  icon_container: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    padding: 10,
  },
  pButton: {
    backgroundColor: "#FDFDFD",
    width: "100%",
    padding: Platform.OS === "ios" ? 13 : 10,
    borderRadius: 8,
    alignItems: "center",
  },
  startButton: {
    width: "48%",
    padding: Platform.OS === "ios" ? 13 : 10,
    borderRadius: 8,
    alignItems: "center",
  },
  buttonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
  pButtonText: {
    fontWeight: "700",
    fontSize: 16,
  },
  infoWrap: {
    display: "flex",
    justifyContent: "center",
    alignItems: "flex-start",
    backgroundColor: "#E8E8E8",
    borderRadius: 8,
    padding: 20,
  },
  info: {
    color: "#808080",
  },
});
