import React from "react";
import {
  StyleSheet,
  Text,
  View,
  Pressable,
  Platform,
  Dimensions,
} from "react-native";
import { useTranslation } from 'react-i18next';

const StartScreen = ({ startTest, timeIsUpText, seeResults }) => {
  const { t } = useTranslation();
  return (
    <View style={styles.container}>
      <View style={styles.infoWrap}>
        <Text style={styles.HeadInfo}>
          {timeIsUpText ? t('timesUp') : t('ready')}
        </Text>
        {timeIsUpText ? (
          <View>
            <Text style={styles.info}>{t('goodJob')}</Text>
            <Text style={styles.info}>{t('finishTest')}</Text>
          </View>
        ) : (
          <Text style={styles.info}>{t('testTimeInfo')}</Text>
        )}
      </View>
      <View style={styles.buttonContainer}>
        <Pressable
          onPress={timeIsUpText ? seeResults : startTest}
          style={styles.button}
        >
          <Text style={styles.buttonText}>
            {timeIsUpText ? t('showResults') : t('startTest')}
          </Text>
        </Pressable>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  button: {
    backgroundColor: "rgb(70, 121, 188)",
    width: "100%",
    padding: Platform.OS === "ios" ? 17 : 14,
    borderRadius: 10,
    alignItems: "center",
  },
  buttonContainer: {
    width: "90%",
    marginVertical: 25,
  },
  buttonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
    fontFamily: "Avenir"
  },
  infoWrap: {
    display: "flex",
    flex: 1,
    width: "90%",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    marginTop: 20,
    marginBottom: 20,
  },
  info: {
    color: "#808080",
    fontSize: 18,
    fontFamily: "Avenir"
  },
  HeadInfo: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 20,
    fontFamily: "Avenir"
  },
});

export default React.memo(StartScreen);
