import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import {
  View,
  StyleSheet,
  Text,
  Dimensions,
  Animated,
  Platform,
  ActivityIndicator,
  Image,
} from "react-native";
import { doc, Timestamp, getDoc, setDoc } from "firebase/firestore";
import { db } from "../../firebaseConfig";
import { storage } from "../mmkvStorage";
import { useFetchSentencesQuery } from "../store/sentencesSlice";
import { FlashList } from "@shopify/flash-list";
import { useHeaderHeight } from "@react-navigation/elements";
import { setScore, decreaseScore } from "../store/scoreSlice";
import { setTimes, setClickedItemTime } from "../store/timerSlice";
import { setCorrectAnswer, setTestId } from "../store/answerSlice";
import { setTestTime, setTimerRunning, reset } from "../store/testTimerSlice";
import { useDispatch, useSelector } from "react-redux";
import Sentence from "../components/Sentence";
import StartScreen from "./StartScreen";
import { addTimesToDB } from "../utils";
import { addResultsToDB, logEvent } from "../utils";
import uuid from "react-native-uuid";
import * as ScreenOrientation from "expo-screen-orientation";
import { setOrientation } from "../store/orientationSlice";
import flip from "../../assets/flip.png";
import Separator from "../components/Separator";
import CustomBottomSheet from "../components/CustomBottomSheet";
import { useTranslation } from 'react-i18next';

const MainScreen = ({ navigation }) => {
  const { t } = useTranslation();
  const { times, clickedItemTime } = useSelector((state) => state.timer);
  const lang = useSelector((state) => state.lang.value);
  const userId = useSelector((state) => state.score.userId);
  const score = useSelector((state) => state.score.value);
  const orientation = useSelector((state) => state.orientation.value);
  const { testTime, isRunning } = useSelector((state) => state.testTimer);
  const { correctAnswer, testId } = useSelector((state) => state.answer);
  const [skip, setSkip] = React.useState(true);
  const {
    data,
    isLoading,
    isError,
    currentData,
    error,
    isFetching,
    isUninitialized,
    refetch,
  } = useFetchSentencesQuery(lang, { skip });
  const [sentenceOfwords, setSentenceOfwords] = useState([]);
  const [clicks, setClicks] = useState([]);
  const headerHeight = useHeaderHeight();
  const [windowDims, setWindow] = useState(Dimensions.get("window"));
  const [sentenceIndex, setSentenceIndex] = useState(0);
  const [countWrongSentences, setCountWrongSentences] = useState([]);
  const [countCorrectSentences, setCountCorrectSentences] = useState([]);
  const [scoreRes, setScoreRes] = useState(false);
  const animatedValueRef = useRef(new Animated.Value(0)).current;
  const ref = useRef(null);
  const refElem = useRef(0);
  const [timer, setTimer] = useState(null);
  const [startChainTime, setStartChainTime] = useState(null);
  const [scrollDown, setScrollDown] = useState(false);
  const [firstClicks, setFirstClicks] = useState({});
  const [timeId, setTimeId] = useState("");
  const [allowToMoveDown, setAllowToMoveDown] = useState(true);
  const dispatch = useDispatch();
  const [timeIsUpText, setTimeIsUpText] = useState(false);
  const windowHeight = Dimensions.get("window").height;
  const [loadMore, setLoadMore] = useState(10);
  const [loadDataFromIndex, setLoadDataFromIndex] = useState(6);
  const [trackedItems, setTrackedItems] = useState([]);
  const [effectHasRun, setEffectHasRun] = useState(false);
  const [timerIsOn, setTimerIsOn] = useState(false);
  const [lastRemovedItem, setLastRemovedItem] = useState(null);
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(true);
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  

  useEffect(() => {
    resetLocalStorge();
    checkOrientation();
    const subscription = ScreenOrientation.addOrientationChangeListener(
      handleOrientationChange
    );
    return () => {
      ScreenOrientation.removeOrientationChangeListeners(subscription);
    };
  }, []);

  const checkOrientation = async () => {
    if (orientation === "landscape" && isFirstLoad) {
    setIsBottomSheetVisible(true);
    setIsFirstLoad(false);
  }
    const orientation = await ScreenOrientation.getOrientationAsync();
    getScreenOrientation();
  };

  const handleOrientationChange = (o) => {
    getScreenOrientation();
  };

  const userAgentString = navigator.userAgent;
  let os15Agent = null;
  let safariAgent = null;
  if (userAgentString !== undefined) {
    os15Agent = userAgentString.indexOf("iPhone OS 15") > -1;
    safariAgent = userAgentString.indexOf("Chrome") > -1;
  }

  useEffect(() => {
    arrangeWords();
  }, [data, loadMore]);

  useEffect(() => {
    const handleChange = ({ screen, window: win }) => {
      setWindow(win);
    };

    const subscription = Dimensions.addEventListener("change", handleChange);
    return () => {
      subscription.remove();
    };
  }, [setWindow]);

  useEffect(() => {
    if (orientation === "landscape" && !timerIsOn ) {
      setSkip(false);
      setIsBottomSheetVisible(true);
    } else if (orientation === "portrait" && isFirstLoad) {
      setIsBottomSheetVisible(true);
    }else{
      setIsBottomSheetVisible(false);
    }
  }, [orientation, timerIsOn]);

  const getScreenOrientation = () => {
    if (Math.abs(window.orientation) === 90) {
      dispatch(setOrientation("landscape"));
    } else {
      dispatch(setOrientation("portrait"));
    }
  };

  useEffect(() => {
    if (trackedItems.length >= 3) {
      const firstItem = trackedItems.shift();
      const correctAnswerToSubmit = countCorrectSentences.find(
        (item) => item.id === firstItem.id
      );

      const wrongeAnswerToSubmit = countWrongSentences.find(
        (item) => item.id === firstItem.id
      );

      addResultsToDB(
        data,
        correctAnswerToSubmit,
        wrongeAnswerToSubmit,
        userId,
        testId
      );
    }
  }, [trackedItems]);

  useEffect(() => {
    if (testTime <= 0) {
      setTimeIsUpText(true);
      clearInterval(timer);
      onTimeUp();
    }
  }, [testTime]);

  useEffect(() => {
    // Run the effect only if sentenceOfwords has data and the effect hasn't run yet
    if (sentenceOfwords.length > 0 && !effectHasRun) {
      // Create a fake viewable items object
      const initialViewableItems = sentenceOfwords.slice(0, 1).map((item) => ({
        item,
        isViewable: true,
        key: item.id,
        index: sentenceOfwords.indexOf(item),
      }));

      onViewableItemsChanged({
        viewableItems: initialViewableItems,
        changed: [],
      });
      setEffectHasRun(true); // Mark the effect as run
    }
  }, [sentenceOfwords, effectHasRun, onViewableItemsChanged]);

  const arrangeWords = () => {
    let testData = [];
    if (data) {
      testData = data.slice(6, 61);
    }
    testData.map((item) => {
      let arrayOfWords = item.sentence.split(" ");
      setSentenceOfwords((prev) => [
        ...prev,
        { id: item.id, arrayOfWords, correctPosition: item.correctPosition },
      ]);
    });
  };

  const loadMoreData = useCallback(() => {
    setLoadDataFromIndex((prev) => prev + 4);
    setLoadMore((prev) => prev + 4);
  }, [loadDataFromIndex, loadMore]);

  const findDuplicateSentences = (arr) => {
    const duplicates = arr.reduce((acc, obj) => {
      acc[obj.sentence] = (acc[obj.sentence] || 0) + 1;
      return acc;
    }, {});

    const result = arr.filter((obj) => {
      if (duplicates[obj.sentence] > 1) {
        duplicates[obj.sentence] = 1;
        return true;
      }
      return false;
    });

    return result;
  };

  const resetLocalStorge = async () => {
    storage.delete("letter_id");
  };

  const startTimer = useCallback(async () => {
    setIsBottomSheetVisible(false);
    setTimerRunning(true);
    setTimer(
      setInterval(() => {
        dispatch(setTestTime());
      }, 1000)
    );
    const uniqueTestId = uuid.v4();
    dispatch(setTestId(uniqueTestId));
    const doRef = doc(db, `UsersTests/${userId}/Tests`, uniqueTestId);
    const docSnap = await getDoc(doRef);
    await setDoc(doRef, {
      ...docSnap.data(),
      testId: uniqueTestId,
      date: Timestamp.fromDate(new Date()),
      score: 0,
      language: lang,
      correctSentenceAnswers: [],
      wrongSentenceAnswers: [],
      test_Type: "sentences",
    });
    setTimerIsOn(true);
    await logEvent("Sentences test started", userId, uniqueTestId);
  }, []);

  const onTimeUp = useCallback(async () => {
    setTimerRunning(false);
    const lastCorrectAnswerToSubmit = countCorrectSentences.pop();
    const lastWrongAnswerToSubmit = countWrongSentences.pop();
    addResultsToDB(
      data,
      lastCorrectAnswerToSubmit,
      lastWrongAnswerToSubmit,
      userId,
      testId
    );
    await logEvent(
      "Sentences test finished",
      data,
      lastCorrectAnswerToSubmit,
      lastWrongAnswerToSubmit,
      userId,
      testId
    );
    if (Platform.OS !== "web") {
      navigation.replace("Result");
      storage.delete("letter_id");
    } else {
      setIsBottomSheetVisible(true);
      storage.delete("letter_id");
    }
  }, [countCorrectSentences, countWrongSentences]);

  const changeOrientation = async (newOrientation) => {
    await ScreenOrientation.lockAsync(newOrientation);
  };

  const seeResults = async () => {
    navigation.replace("Result", { lang });
    // await Linking.openURL("result").catch((err) =>
    //   console.error("An error occurred", err)
    // );
    changeOrientation(2);
  };

  const onWordPressed = async (value, clickTime) => {
    // If it hasn't been clicked, set its value in the "firstClicks" object to "true"
    const storedElement = storage.getString("letter_id");
    let clicksFromStorage = [];

    if (!firstClicks[value.sentenceKey]) {
      setFirstClicks({ ...firstClicks, [value.sentenceKey]: true });
    }

    if (storedElement === undefined) {
      clicksFromStorage.push(value);
    } else {
      clicksFromStorage = JSON.parse(storedElement);

      if (
        !clicksFromStorage.some(
          (e) =>
            e.sentenceKey == value.sentenceKey && e.wordKey == value.wordKey
        )
      ) {
        const linesArrlength = getOccurrence(
          clicksFromStorage,
          value.sentenceKey
        );

        if (linesArrlength > 0) {
          clicksFromStorage.forEach((arrElm, indxElm) => {
            if (
              !(value.wordKey > arrElm.wordKey + 1) &&
              value.sentenceKey == arrElm.sentenceKey &&
              !(value.wordKey < arrElm.wordKey - 1) &&
              value.sentenceKey == arrElm.sentenceKey
            ) {
              clicksFromStorage.splice(
                clicksFromStorage.findIndex(
                  (e) =>
                    !(value.wordKey > e.wordKey + 1) &&
                    value.sentenceKey == e.sentenceKey &&
                    !(value.wordKey < e.wordKey - 1) &&
                    value.sentenceKey == e.sentenceKey
                ),
                1
              );
            }
          });
        }

        clicksFromStorage.push(value);
      } else if (
        clicksFromStorage.some(
          (e) =>
            e.wordKey === value.wordKey && e.sentenceKey === value.sentenceKey
        )
      ) {
        const removedItem = clicksFromStorage.splice(
          clicksFromStorage.findIndex(
            (e) =>
              e.wordKey === value.wordKey && e.sentenceKey === value.sentenceKey
          ),
          1
        );
        setLastRemovedItem(removedItem);
      }

      const linesArrlength = getOccurrence(
        clicksFromStorage,
        value.sentenceKey
      );

      if (value.index == 0) {
        if (linesArrlength == 2 && allowToMoveDown) {
          setTimeout(() => {
            setScrollDown(true);
            ref.current?.scrollToIndex({
              index: value.index + 1,
              animated: true,
              viewOffset: windowDims.height - 20,
              viewPosition: 1,
            });
          }, 200);
          setAllowToMoveDown(false);
        }
      } else {
        if (linesArrlength == 2) {
          setTimeout(() => {
            setScrollDown(true);
            if (ref.current) {
              ref.current?.scrollToIndex({
                index: value.index + 1,
                animated: true,
                viewPosition: 1,
              });
            }
          }, 200);
        }
      }
    }

    setClicks(clicksFromStorage);

    setSentenceIndex(value.index);

    storage.set("letter_id", JSON.stringify(clicksFromStorage));

    getScore(clicksFromStorage, value, clickTime);
  };

  const getOccurrence = (array, value) => {
    return array.filter((v) => v.sentenceKey === value).length;
  };

  const getScore = async (arr, val, clickTime) => {
    if (arr.length > 0 && clickTime) {
      const currentSentenceLines = arr.filter(
        (sentence, index) => sentence.sentenceKey === val.sentenceKey
      );
      let checkIfIsCorrectAnswer;
      let currentSentence;
      let hasOneCorrectLine;
      currentSentenceLines.forEach(
        (sc) => (currentSentence = data.filter((v) => sc.sentenceKey === v.id))
      );
      currentSentence &&
        currentSentence.length > 0 &&
        dispatch(
          setClickedItemTime({ id: currentSentence[0].id, time: clickTime })
        );
      let result = currentSentenceLines.map((a) => a.wordKey);

      result.sort((a, b) => a - b);

      if (currentSentence && currentSentence.length > 0) {
        hasOneCorrectLine = checkCommonElements(
          result,
          currentSentence[0].correctPosition
        );
        checkIfIsCorrectAnswer = areEqual(
          result,
          currentSentence[0].correctPosition
        );
      }

      if (currentSentenceLines.length > 2) {
        dispatch(
          setCorrectAnswer({ isCorrect: false, value: hasOneCorrectLine })
        );
        setScoreRes(!scoreRes);
        setCountCorrectSentences((prev) =>
          prev.filter((elm) => elm.sentence !== currentSentence[0].sentence)
        );
        setCountWrongSentences((prev) => [
          ...prev,
          {
            id: currentSentence[0].id,
            time: clickTime,
            sentence: currentSentence[0].sentence,
          },
        ]);
      } else if (currentSentenceLines.length === 2) {
        if (
          countCorrectSentences.some(
            (item) => item.sentence === currentSentence[0].sentence
          )
        ) {
          if (hasOneCorrectLine === 1 || hasOneCorrectLine === 3) {
            setCountWrongSentences((prev) =>
              prev.filter(
                (item) => item.sentence !== currentSentence[0].sentence
              )
            );
            setCountCorrectSentences((prev) =>
              prev.filter((elm) => elm.sentence !== currentSentence[0].sentence)
            );
            setCountCorrectSentences((prev) => [
              ...prev,
              {
                id: currentSentence[0].id,
                sentence: currentSentence[0].sentence,
                answerValue: hasOneCorrectLine,
                chainLines: currentSentenceLines.length,
                time: clickTime,
              },
            ]);
          } else {
            if (
              !countWrongSentences.some(
                (item) => item.sentence === currentSentence[0].sentence
              )
            ) {
              setCountCorrectSentences((prev) =>
                prev.filter(
                  (item) => item.sentence !== currentSentence[0].sentence
                )
              );
              setCountWrongSentences((prev) => [
                ...prev,
                {
                  id: currentSentence[0].id,
                  time: clickTime,
                  sentence: currentSentence[0].sentence,
                },
              ]);
            }
          }
        } else {
          if (hasOneCorrectLine === 1 || hasOneCorrectLine === 3) {
            dispatch(
              setCorrectAnswer({ isCorrect: true, value: hasOneCorrectLine })
            );
            setCountCorrectSentences((prev) => [
              ...prev,
              {
                id: currentSentence[0].id,
                sentence: currentSentence[0].sentence,
                answerValue: hasOneCorrectLine,
                chainLines: currentSentenceLines.length,
                time: clickTime,
              },
            ]);
            setCountWrongSentences((prev) =>
              prev.filter(
                (item) => item.sentence !== currentSentence[0].sentence
              )
            );
          } else {
            dispatch(
              setCorrectAnswer({ isCorrect: false, value: hasOneCorrectLine })
            );
            setCountCorrectSentences((prev) =>
              prev.filter((elm) => elm.sentence !== currentSentence[0].sentence)
            );
            setCountWrongSentences((prev) => [
              ...prev,
              {
                id: currentSentence[0].id,
                time: clickTime,
                sentence: currentSentence[0].sentence,
              },
            ]);
            console.log("Not correct answer");
            setScoreRes(!scoreRes);
          }
        }
      }
    }
  };

  function areEqual(array1, array2) {
    if (array1.length === array2.length) {
      return array1.every((element, index) => {
        if (element === array2[index]) {
          return true;
        }

        return false;
      });
    }

    return false;
  }

  function checkCommonElements(arr1, arr2) {
    let commonElements = arr1.filter((element) => arr2.includes(element));

    if (commonElements.length === 1) {
      return 1;
    } else if (commonElements.length === 2) {
      return 3;
    } else {
      return 0; // No common elements or more than two common elements
    }
  }

  const onViewableItemsChanged = useCallback(({ viewableItems }) => {
    const onScreenItems = viewableItems.slice(-1);
    setTimeout(() => {
      setScrollDown(false);
    }, 100);
    onScreenItems.forEach((visible) => {
      setTrackedItems((prev) => [
        ...prev,
        { ...visible.item, visible: visible.isViewable },
      ]);
    });
  }, []);

  const scroll_distance = windowDims.height;

  const keyExtractor = useCallback((item, i) => `${i}-${item.id}`, []);

  const renderItem = ({ item, index }) => {
    const animheight = animatedValueRef.interpolate({
      inputRange: [0, 20],
      outputRange: [scroll_distance, scroll_distance / 2],
      extrapolate: "clamp",
    });
    return (
      <Animated.View
        ref={refElem}
        style={{
          width: windowDims.width,
          height: animheight,
          alignItems: "center",
          justifyContent: "center",
          paddingLeft: 30,
          paddingRight: 30,
        }}
      >
        <Sentence
          sentence={item}
          index={index}
          sentenceKey={item.id}
          onWordPressed={onWordPressed}
          windowDims={windowDims}
          clicks={clicks}
          headerHeight={headerHeight}
          trackedItems={trackedItems}
          timerIsOn={timerIsOn}
          lastRemovedItem={lastRemovedItem}
        />
      </Animated.View>
    );
  };

  return (
    <View style={[styles.container]} id={"fallscreen_id"}>
      {error ? (
        <Text>There was an error</Text>
      ) : isLoading || sentenceOfwords.length === 0 ? (
        <ActivityIndicator
          size="large"
          color="#0000ff"
          style={{ marginTop: (windowDims.height - headerHeight) / 2 }}
        />
      ) : (
        <FlashList
          data={sentenceOfwords}
          renderItem={renderItem}
          extraData={[
            clicks,
            correctAnswer,
            countCorrectSentences,
            countWrongSentences,
            clickedItemTime,
          ]}
          estimatedItemSize={180}
          keyExtractor={keyExtractor}
          ref={ref}
          scrollEnabled={scrollDown}
          initialScrollIndex={sentenceIndex}
          showsVerticalScrollIndicator={false}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { y: animatedValueRef } } }],
            { useNativeDriver: false }
          )}
          contentOffset={{ x: 0, y: scroll_distance }}
          viewabilityConfig={{
            waitForInteraction: true,
            itemVisiblePercentThreshold: 95,
            minimumViewTime: 250,
          }}
          onViewableItemsChanged={onViewableItemsChanged}
          ItemSeparatorComponent={() => <Separator />}
          // onEndReached={loadMoreData}
          onEndReachedThreshold={0.1}
        />
      )}

      <CustomBottomSheet
        isVisible={isBottomSheetVisible}
        height={timeIsUpText ? "100%" : "70%"}
      >
        {orientation === "portrait" ? (
          <View style={styles.infoWrap}>
            <Image source={flip} style={{ width: 125, height: 125 }} />
            <Text style={styles.info}>
              {t("rotateYourPhone")}
            </Text>
          </View>
        ) : orientation === "landscape" && isFirstLoad && !timerIsOn ? (
          <StartScreen
            startTest={startTimer}
            timeIsUpText={timeIsUpText}
            seeResults={seeResults}
          />
        ) : null}
      </CustomBottomSheet>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
  },
  header: {
    fontSize: 16,
    marginVertical: 10,
  },

  fadingContainer: {
    height: 19,
    with: 10,
    backgroundColor: "gray",
  },
  display: {
    flex: 1 / 24,
    justifyContent: "center",
    alignItems: "center",
  },
  displayText: {
    color: "#000",
    fontSize: 18,
    fontWeight: "200",
    fontFamily: Platform.OS === "ios" ? "Helvetica Neue" : null,
  },
  infoWrap: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
    marginBottom: 20,
  },
  info: {
    color: "#808080",
    paddingTop: 30,
    textAlign: "center",
  },
  button: {
    backgroundColor: "rgb(70, 121, 188)",
    width: "100%",
    padding: Platform.OS === "ios" ? 17 : 14,
    borderRadius: 10,
    alignItems: "center",
  },
  buttonContainer: {
    width: "50%",
    marginVertical: 25,
  },
  buttonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
});

export default React.memo(MainScreen);
