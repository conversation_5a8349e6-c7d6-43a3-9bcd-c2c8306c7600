import React, { useEffect } from "react";
import {
  StyleSheet,
  Platform,
  View,
  ActivityIndicator,
  Image,
  Text,
  Pressable,
} from "react-native";
import { useTranslation } from "react-i18next";
import { setTestType } from "../store/testTypeSlice";
import { useDispatch } from "react-redux";
import AboutScreen from "./AboutScreen";
import InstructionsScreen from "./InstructionsScreen";
import flip from "../../assets/flip.png";
import { setLang } from "../store/langSlice";
import { getLocales } from "expo-localization";

const deviceLanguage = getLocales()[0].languageCode;

const HomeScreen = ({ route, navigation }) => {
  const { i18n, t } = useTranslation();
  const dispatch = useDispatch();
  const { standalone, lang } = route.params ?? {};

  useEffect(() => {
    if (route.params && standalone) {
      dispatch(setTestType(route.params.standalone));
    }
    if (route.params && lang) {
      const userLang = lang.toLowerCase();
      i18n.changeLanguage(userLang);
      dispatch(setLang(userLang));
    } else {
      i18n.changeLanguage(deviceLanguage);
      dispatch(setLang(deviceLanguage));
    }
  }, []);

  useEffect(() => {
    // Handle language change from route params if present
    if (route.params?.lang) {
      const userLang = route.params.lang.toLowerCase();
      dispatch(setLang(userLang));
    }
  }, [route.params?.lang]);

  const continueToSentencesTest = () => {
    if (route.params && route.params.standalone === "false") {
      navigation.navigate("Instructions", { ...route.params });
    } else {
      navigation.navigate("About", { ...route.params });
    }
  };

  return (
    <View
      style={
        (styles.infoWrap,
        {
          alignItems: "center",
          justifyContent: "space-around",
          flex: 1,
          width: "100%",
        })
      }
    >
      <Image source={flip} style={{ width: 125, height: 125 }} />
      <Text
        style={
          (styles.info,
          { paddingHorizontal: 20, fontSize: 18, textAlign: "center" })
        }
      >
        {t("landscapeText")}
      </Text>
      <View style={styles.buttonContainer}>
        <Pressable onPress={continueToSentencesTest} style={styles.button}>
          <Text style={styles.buttonText}>{t("continue")}</Text>
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  button: {
    backgroundColor: "rgb(70, 121, 188)",
    width: "100%",
    padding: Platform.OS === "ios" ? 17 : 14,
    borderRadius: 10,
    alignItems: "center",
  },
  buttonContainer: {
    width: "90%",
    marginVertical: 25,
  },
  infoWrap: {
    display: "flex",
    flex: 1,
    width: "90%",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    marginTop: 20,
    marginBottom: 20,
  },
  info: {
    color: "#808080",
    fontSize: 18,
  },
  HeadInfo: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 20,
  },
  buttonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
});

export default React.memo(HomeScreen);
