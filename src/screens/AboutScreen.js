import { StyleSheet, Text, View, Pressable, Platform } from "react-native";
import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from "react";
import {
  collection,
  addDoc,
  query,
  where,
  getDocs,
  doc,
} from "firebase/firestore";
import { db } from "../../firebaseConfig";
import { setLang } from "../store/langSlice";
import { useDispatch, useSelector } from "react-redux";
import { useFonts } from "expo-font";
import { setUserId } from "../store/scoreSlice";
import RegisterScreen from "./RegisterScreen";
import { getLocales } from "expo-localization";
import { useTranslation } from "react-i18next";
import CustomBottomSheet from "../components/CustomBottomSheet";

const deviceLanguage = getLocales()[0].languageCode;

const AboutScreen = ({ route, navigation }) => {
  const { userId } = useSelector((state) => state.answer);
  const dispatch = useDispatch();
  const { email, age, lang } = route.params ?? {};
  const { t, i18n } = useTranslation();
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);

  useEffect(() => {
    if (route.params && lang) {
      const userLang = lang.toLowerCase();
      i18n.changeLanguage(userLang);
      dispatch(setLang(userLang));
    } else {
      i18n.changeLanguage(deviceLanguage);
      dispatch(setLang(deviceLanguage));
    }
  }, []);

  useEffect(() => {
    (async () => {
      if (route.params && userInfoValidation(email) && ageValidation(age)) {
        const userFromDB = await getUserByEmail(email);
        if (userFromDB) {
          dispatch(setUserId(userFromDB.id));
        } else {
          const docRef = await addDoc(collection(db, "UsersTests"), {
            email: email ?? "",
            age: age ?? "",
            lang: lang ?? "",
          });
          dispatch(setUserId(docRef.id));
        }
      } else {
        setIsBottomSheetVisible(true);
      }
    })();
  }, []);

  const ageValidation = (userAge) => {
    const parsedAge = parseInt(userAge, 10);
    if (parsedAge && parsedAge > 3 && parsedAge < 100) {
      return true;
    } else {
      return false;
    }
  };

  const userInfoValidation = (userEmail) => {
    const emailRegex =
      /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;
    const nameRegex = /^[a-zA-ZäöåÄÖÅ\s]+$/;
    if (!userEmail || emailRegex.test(userEmail) === false) {
      return false;
    }
    return true;
  };

  const getUserByEmail = async (email) => {
    const userRef = collection(db, "UsersTests");

    const q = query(userRef, where("email", "==", email));

    const querySnapshot = await getDocs(q);
    if (querySnapshot.empty) {
      console.log("No document found for the given email");
      return null;
    }

    const documentSnapshot = querySnapshot.docs[0];
    const userData = { id: documentSnapshot.id, ...documentSnapshot.data() };
    const subcollectionRef = collection(
      doc(db, "UsersTests", documentSnapshot.id),
      "Tests"
    );
    const subcollectionSnapshot = await getDocs(subcollectionRef);
    const subcollectionData = subcollectionSnapshot.docs.map((doc) =>
      doc.data()
    );

    userData.subcollectionData = subcollectionData;
    return userData;
  };

  const snapPoints = useMemo(() => ["25%", "90%"], []);

  const handleSheetChanges = useCallback((index) => {}, []);

  const navtoInstraction = async () => {
    navigation.replace("Instructions");
  };

  const [fontsLoaded] = useFonts({
    Avenir: require("../../assets/fonts/Avenir-Regular.ttf"),
  });

  if (!fontsLoaded) {
    return null;
  }

  return (
    <View style={[styles.wrapper]}>
      <Text lang={lang} style={styles.infoText}>
        <Text lang={lang} style={styles.infoTitles}>
          {t("aboutTest")}{" "}
        </Text>
        {t("About")}
      </Text>
      <Text lang={lang} style={styles.infoText}>
        <Text lang={lang} style={styles.infoTitles}>
          {t("timeLimit")}:{" "}
        </Text>
        {t("timeLimitText")}
      </Text>
      <Text lang={lang} style={styles.infoText}>
        <Text lang={lang} style={styles.infoTitles}>
          {t("Note")}:{" "}
        </Text>
        {t("NoteText")}
      </Text>
      <Pressable
        style={[
          styles.button,
          {
            backgroundColor: userId === null ? "#a6b9d2" : "#4679BC",
          },
        ]}
        onPress={navtoInstraction}
        disabled={userId === null}
      >
        <Text lang={lang} style={styles.buttonText}>
          {t("toInstruction")}
        </Text>
      </Pressable>
      <CustomBottomSheet
        isVisible={isBottomSheetVisible}
        onClose={() => setIsBottomSheetVisible(false)}
        height="100%"
      >
        <RegisterScreen
          setIsBottomSheetVisible={setIsBottomSheetVisible}
          getUserByEmail={getUserByEmail}
        />
      </CustomBottomSheet>
    </View>
  );
};

export default AboutScreen;

const styles = StyleSheet.create({
  wrapper: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    height: "100%",
    backgroundColor: "#f9f9f9",
    padding: 24,
  },
  lang_link: {
    paddingLeft: 5,
  },
  infoText: {
    fontFamily: "Avenir",
    fontSize: 18,
    color: "#4E4E4E",
  },
  infoTitles: {
    fontWeight: "600",
    color: "#1A3F71",
  },
  icon: {
    width: 30,
    height: 30,
    marginRight: 15,
  },
  icon_container: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    backgroundColor: "#E5EEFA",
    paddingLeft: 25,
    paddingVertical: 5,
    marginVertical: 2,
    borderRadius: 3,
    width: "100%",
  },
  button: {
    width: "100%",
    padding: Platform.OS === "ios" ? 18 : 12,
    borderRadius: 10,
    marginBottom: 20,
    alignItems: "center",
  },
  buttonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
  infoWrap: {
    display: "flex",
    justifyContent: "center",
    alignItems: "flex-start",
    borderRadius: 8,
    width: "100%",
  },
  info: {
    color: "#808080",
  },
});
