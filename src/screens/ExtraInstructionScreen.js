import React from "react";
import {
  StyleSheet,
  Text,
  View,
  Pressable,
  Platform,
  Image,
} from "react-native";
import { useTranslation } from "react-i18next";
import close from "../../assets/close.png";
import { useSelector } from "react-redux";
import { shadow } from "../components/Shadow";

const ExtraInstructionScreen =  ({ closeHelpScreen }) => {
  const { t, i18n } = useTranslation();
  const lang = useSelector((state) => state.lang.value);

  return (
    <View style={styles.container}>
      <Pressable
        hitSlop={30}
        style={{ position: "absolute", right: 25, top: 5 }}
        onPress={closeHelpScreen}
      >
        <Image source={close} style={{ width: 17, height: 17 }} />
      </Pressable>
      <View style={styles.infoWrap}>
        <Text style={styles.HeadInfo}>{t("extraInstructionsHead")}</Text>
        <View style={{display: "flex", flexDirection: "column", justifyContent: "space-between",
    width: "100%"}}>
        <View style={{ display: "flex" }}>
        {t("extraInstructions", { returnObjects: true }).map((elem, index) => {
          return (
            <Text key={index} style={{ display: "flex" }}>
              <Text style={styles.instrListType}>{index + 1}.</Text>
              <Text key={index} style={styles.instrInfoText}>
                {" "}
                {elem}
              </Text>
            </Text>
          );
        })}
        </View>
        <View >
          <Text style={styles.example}>{t("example")}</Text>
          <View style={styles.imgInstruction}>
            <View style={[styles.imgStyle, styles.dishShadow]}><Image
              source={t("extraInstructionsImg_1", { returnObjects: true })}
              style={{width: 327, height: 70 }}
            />
            </View>
            <View style={[styles.imgStyle, styles.dishShadow]}><Image
              source={t("extraInstructionsImg_2", { returnObjects: true })}
              style={{width: 327, height: 70 }}
            />
            </View>
          </View>
        </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
    marginBottom: 20,
  },
  button: {
    backgroundColor: "rgb(70, 121, 188)",
    width: "100%",
    padding: Platform.OS === "ios" ? 17 : 14,
    borderRadius: 10,
    alignItems: "center",
  },
  buttonContainer: {
    width: "90%",
    marginVertical: 25,
  },
  infoWrap: {
    display: "flex",
    flex: 1,
    width: "90%",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    marginTop: 20,
    marginBottom: 20,
  },
  info: {
    color: "#808080",
    fontSize: 18,
  },
  HeadInfo: {
    display: "flex",
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 20,
  },
  buttonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
  instrInfoText: {
    fontFamily: "Avenir",
    fontSize: 18,
    color: "#4E4E4E",
    lineHeight: 28,
    display: "flex",
  },
  instrListType: {
    fontFamily: "Avenir",
    fontSize: 18,
    color: "#4E4E4E",
    lineHeight: 28,
    marginRight: 5,
  },
  example: {
    fontFamily: "Avenir",
    fontSize: 17,
    color: "#4E4E4E",
    marginVertical: 15
  },
  imgInstruction: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    paddingHorizontal: 27,
  paddingVertical: 10,
  },
  imgStyle: {
    backgroundColor: "#FFFFFF",
    width: 327, 
    height: 70 
  },
  dishShadow: {
    ...shadow({
      elevation: 13,
      rgba: "rgba(0, 0, 0, 0.099)",
      opacity: 0.099,
      radius: 20,
      offsetWidth: 1,
      offsetHeight: 1
    })
  },
  
});

export default React.memo(ExtraInstructionScreen);
