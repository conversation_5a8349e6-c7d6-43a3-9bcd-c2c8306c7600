import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  Text,
  ScrollView,
  View,
  Platform,
  TextInput,
  KeyboardAvoidingView,
  Pressable,
  Animated,
  Modal,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import {
  collection,
  addDoc,
  query,
  where,
  getDocs,
  updateDoc,
  doc,
} from "firebase/firestore";
import { db } from "../../firebaseConfig";
import { setUserId } from "../store/scoreSlice";
import i18n from "../Translations";
import ErrorModal from "../components/ErrorModal";

function RegisterScreen({
  navigation,
  setIsBottomSheetVisible,
  getUserByEmail,
}) {
  const lang = useSelector((state) => state.lang.value);
  const { userId } = useSelector((state) => state.answer);
  const [userEmail, setUseremail] = useState("");
  const [fName, setFname] = useState("");
  const [modalVisible, setModalVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const dispatch = useDispatch();
  const [userAge, setUerAge] = useState("");
  const [emailExist, setEmailExist] = useState(false);
  const [isForwarding, setIsForwarding] = useState(false);

  const ageValidation = () => {
    const parsedAge = parseInt(userAge, 10);
    if (parsedAge && parsedAge > 3 && parsedAge < 100) {
      return true;
    } else {
      setErrorMessage(i18n.t("validAge"));
      setModalVisible(true);
      return false;
    }
  };

  const userInfoValidation = () => {
    const emailRegex =
      /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;
    const nameRegex = /^[a-zA-ZäöåÄÖÅ\s]+$/;
    if (!userEmail || emailRegex.test(userEmail) === false) {
      setErrorMessage(i18n.t("validEmail"));
      setModalVisible(true);
      return false;
    }
    if (!fName || nameRegex.test(fName) === false || fName.length > 30) {
      setErrorMessage(i18n.t("validName"));
      setModalVisible(true);
      return false;
    }
    return true;
  };

  const handleRegister = async () => {
    const emailExist = await checkDocumentExistence("email", userEmail.trim());

    if (userInfoValidation() && ageValidation()) {
      if (!emailExist) {
        const docRef = await addDoc(collection(db, "UsersTests"), {
          email: userEmail.trim().toLowerCase(),
          firstName: fName,
          age: userAge.trim(),
        });
        dispatch(setUserId(docRef.id));
        setErrorMessage(i18n.t("registerSuccess"));
        setModalVisible(true);
        setIsForwarding(true);
        setTimeout(() => {
          setIsBottomSheetVisible(false);
        }, 3000);
      } else {
        const userFromDB = await getUserByEmail(userEmail);
        dispatch(setUserId(userFromDB.id));
        setErrorMessage(i18n.t("alreadyReg"));
        setModalVisible(true);
        setEmailExist(true);
        setIsForwarding(true);
        setTimeout(() => {
          setIsBottomSheetVisible(false);
        }, 3000);
      }
    }
  };

  async function checkDocumentExistence(field, value) {
    let queryEmail = query(
      collection(db, `UsersTests`),
      where(field, "==", value)
    );
    const snapshot = await getDocs(queryEmail);
    if (snapshot.empty) {
      console.log("No matching documents");
      return;
    }

    return true;
  }

  return (
    <KeyboardAvoidingView style={styles.container} behavior="padding">
      <View style={styles.resultItem}>
        <View style={styles.resultSections}>
          <View style={styles.infoWrap}>
            <Text lang={lang} style={styles.infoHead}>
              {i18n.t("enterInfo")}
            </Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              placeholder={i18n.t("firstName")}
              placeholderTextColor="#8f8f8f"
              value={fName}
              onChangeText={(text) => setFname(text)}
              style={styles.input}
            />
            <TextInput
              placeholder={i18n.t("age")}
              placeholderTextColor="#8f8f8f"
              value={userAge}
              inputMode={"numeric"}
              keyboardType={"numeric"}
              maxLength={2}
              onChangeText={(text) => setUerAge(text)}
              style={styles.input}
            />
            <TextInput
              placeholder={i18n.t("email")}
              placeholderTextColor="#8f8f8f"
              value={userEmail}
              inputMode={"email-address"}
              keyboardType={"email-address"}
              onChangeText={(text) => setUseremail(text)}
              style={styles.input}
            />
          </View>
          <View style={styles.buttonContainer}>
            <Pressable onPress={handleRegister} style={styles.button}>
              <Text lang={lang} style={styles.buttonText}>
                {i18n.t("register")}
              </Text>
            </Pressable>
          </View>
        </View>
      </View>
      <ErrorModal
        visible={modalVisible}
        message={errorMessage}
        onClose={() => setModalVisible(false)}
        lang={lang}
        type={emailExist ? "info" : "error"}
        isForwarding={isForwarding}
      />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
  },
  resultItem: {
    flex: 1,
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    height: "100%",
  },
  resultItemText: { color: "#808080" },
  buttonContainer: {
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 22,
    marginBottom: 15,
  },
  resultSections: {
    width: "100%",
  },
  inputContainer: {
    width: "100%",
    marginTop: 10,
  },
  input: {
    backgroundColor: "white",
    paddingHorizontal: 15,
    paddingVertical: Platform.OS === "ios" ? 15 : 13,
    borderRadius: 10,
    marginTop: 15,
    borderColor: "#d6d6d6",
    borderStyle: "solid",
    borderWidth: 1,
  },
  button: {
    backgroundColor: "#4679BC",
    width: "100%",
    padding: Platform.OS === "ios" ? 18 : 15,
    borderRadius: 10,
    alignItems: "center",
  },
  buttonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
  dengerBox: {
    padding: 5,
    backgroundColor: "rgb(255, 222, 222)",
  },
  dengerTxt: {
    color: "#EC3940",
  },
  infoHead: {
    fontSize: 18,
    fontWeight: 600,
  },
});

export default RegisterScreen;
