import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  times: [],
  isRunning: true,
  time: { id: null, seconds: 0, milliseconds: 0 },
  clickedItemTime: {id: null, time: null},
}

export const timerSlice = createSlice({
  name: 'timer',
  initialState,
  reducers: {
    setTimes: (state, action) => ({
      ...state.times,
      times: action.payload
    }),
    setClickedItemTime: (state, action) => ({
      ...state,
      clickedItemTime: {id: action.payload.id, time: action.payload.time} 
    }),
  },
})

export const { setTimes, setClickedItemTime } = timerSlice.actions

export default timerSlice.reducer