import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  value: 0,
  userId: null,
  lettersTestId: null,
  wordsTestId: null,
}

export const scoreSlice = createSlice({
  name: 'score',
  initialState,
  reducers: {
    setScore: (state, action) => ({
      ...state,
      value : action.payload
    }),
    decreaseScore: (state, action) => ({
      ...state,
      value : state.value - action.payload
    }),
    setUserId: (state, action) => ({
      ...state,
      userId : action.payload
    }),
    setLettersTestId: (state, action) => ({
      ...state,
      lettersTestId : action.payload
    }),
    setWordsTestId: (state, action) => ({
      ...state,
      wordsTestId : action.payload
    }),
  },
})

export const { setScore, decreaseScore, setUserId, setLettersTestId, setWordsTestId } = scoreSlice.actions

export default scoreSlice.reducer