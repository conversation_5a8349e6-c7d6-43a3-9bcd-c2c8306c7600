import { createSlice } from '@reduxjs/toolkit'


const initialState = {
    value: null,
  }

  
export const orientationSlice = createSlice({
    name: 'orientation',
    initialState,
    reducers: {
      setOrientation: (state, action) => ({
        ...state.value,
        value : action.payload
      }),
    },
  })

  export const { setOrientation } = orientationSlice.actions
export default orientationSlice.reducer