import { createSlice } from '@reduxjs/toolkit'


const initialState = {
    correctAnswer: {isCorrect: false, value: 0},
    testId: null,
  }
  
export const answerSlice = createSlice({
    name: 'answer',
    initialState,
    reducers: {
      setCorrectAnswer: (state, action) => ({
        ...state,
        correctAnswer : action.payload
      }),
      setTestId: (state, action) => ({
        ...state,
        testId: action.payload
      }),
    },
  })

  export const { setCorrectAnswer, setTestId } = answerSlice.actions
export default answerSlice.reducer