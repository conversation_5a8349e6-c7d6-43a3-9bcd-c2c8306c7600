import { createSlice } from '@reduxjs/toolkit'
import i18n from '../Translations'


const initialState = {
    value: null,
  }

  
export const langSlice = createSlice({
    name: 'lang',
    initialState,
    reducers: {
      setLang: (state, action) =>  {
        i18n.locale = action.payload
        state.value = action.payload;
      },
    },
  })

  export const { setLang } = langSlice.actions
export default langSlice.reducer