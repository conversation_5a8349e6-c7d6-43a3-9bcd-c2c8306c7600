import { configureStore } from "@reduxjs/toolkit";
import { sentencesSlice } from "./sentencesSlice";
import { setupListeners } from "@reduxjs/toolkit/query";
import scoreReducer from './scoreSlice'
import timerReducer from './timerSlice'
import langReducer from './langSlice'
import testTimerReducer from './testTimerSlice'
import answerReducer from './answerSlice'
import orientationReducer from './orientationSlice'
import instructionsReducer from './instructionsSlice'
import testTypeReducer from './testTypeSlice'

export const store = configureStore({
  reducer: {
    [sentencesSlice.reducerPath]: sentencesSlice.reducer,
    score: scoreReducer,
    timer: timerReducer,
    lang: langReducer,
    testTimer: testTimerReducer,
    answer: answerReducer,
    orientation: orientationReducer,
    instructions: instructionsReducer,
    testType: testTypeReducer,
  },
  middleware: (getDefaultMiddleware) =>
getDefaultMiddleware({serializableCheck: false}).concat(sentencesSlice.middleware) 
});

setupListeners(store.dispatch) ;
