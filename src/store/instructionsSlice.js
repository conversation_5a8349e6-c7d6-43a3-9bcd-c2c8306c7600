import { createSlice } from '@reduxjs/toolkit'


const initialState = {
    index: 0,
    scrolledItem: 0
  }
  
export const instructionsSlice = createSlice({
    name: 'instructions',
    initialState,
    reducers: {
      setScrolledItem: (state, action) =>  ({
        ...state,
        scrolledItem: action.payload
      }),
      setIndex: (state, action) => ({
        ...state,
        index: action.payload
      }),
    }
  })

  export const { setScrolledItem, setIndex } = instructionsSlice.actions
export default instructionsSlice.reducer