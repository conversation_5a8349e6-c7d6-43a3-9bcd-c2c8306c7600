import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  testTime: 120,
  isRunning: false,
}

export const testTimerSlice = createSlice({
  name: 'testTimer',
  initialState,
  reducers: {
    setTestTime: (state) => ({
      ...state.testTime,
      testTime: state.testTime - 1
    }),
    setTimerRunning: (state, action) => ({
      ...state.isRunning,
      isRunning: action.payload
  }),
  reset: () => initialState
}
})

export const { setTestTime, setTimerRunning, reset } = testTimerSlice.actions

export default testTimerSlice.reducer