import { createApi, fakeBaseQuery } from "@reduxjs/toolkit/query/react";
import { doc, getDoc } from "firebase/firestore";
import { db } from "../../firebaseConfig";

export const sentencesSlice = createApi({
  reducerPath: "sentencesSlice",
  baseQuery: fakeBaseQuery(),
  // refetchOnFocus: true,
  endpoints: (builder) => ({
    fetchSentences: builder.query({
      async queryFn(lang) {
        const bigLang = lang.toUpperCase()
        try {
          const docRef = doc(db, "SentencesChain", bigLang);
          const docSnap = await getDoc(docRef);
          let sentences = [];
          if (docSnap.exists()) {
            docSnap.data().sentences.forEach((field, index) => {
              sentences.push({
                id: index,
                ...field,
              });
            });
          } else {
            // doc.data() will be undefined in this case
          }
          const smallSen = sentences.filter(
            (item) => item.sentence.length < 100
          );
          const shuffledSentences = shuffle(smallSen);
          const data = shuffledSentences.slice(0, 60);
          return { data: data };
        } catch (err) {
          return { error: err };
        }
      },
    }),
  }),
});

function shuffle(array) {
  var currentIndex = array.length,
    temporaryValue,
    randomIndex;

  // While there remain elements to shuffle...
  while (0 !== currentIndex) {
    // Pick a remaining element...
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex -= 1;

    // And swap it with the current element.
    temporaryValue = array[currentIndex];
    array[currentIndex] = array[randomIndex];
    array[randomIndex] = temporaryValue;
  }

  return array;
}

const easy = {
  min: 0,
  max: 4,
};
const meduim = {
  min: 4.1,
  max: 5.5,
};
const hard = {
  min: 5.6,
  max: 6,
};
const hardest = {
  min: 6.1,
  max: 25,
};

let resOfFilterdAvgWords = (arr, range) => {
  const result = arr.filter(function (o) {
    return o <= range.max && o >= range.min;
  });
  return result;
};

const sortArr = (numArray) => {
  numArray.sort(function (a, b) {
    return a - b;
  });
  return numArray;
};

const average = (arr) => {
  let sum = 0;
  for (let i = 0; i < arr.length; i++) {
    sum += parseInt(arr[i], 10); //don't forget to add the base
  }

  let avg = sum / arr.length;
  return avg;
};

export const { useFetchSentencesQuery } = sentencesSlice;
