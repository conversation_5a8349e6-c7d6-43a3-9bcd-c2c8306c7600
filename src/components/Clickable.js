import {
  View,
  StyleSheet,
  Pressable,
} from "react-native";
import React, { memo } from "react";

const Clickable = ({
  sentenceKey,
  onWordPressed,
  clicks,
  index,
  wordKey,
  easyTimer,
}) => {
 
 

  return (
    <Pressable
      hitSlop={5}
      onPress={(pEvent) => [
        onWordPressed({ sentenceKey, wordKey, index }, 
        easyTimer && 
         !easyTimer.isPaused() && 
        easyTimer.getTimeValues().toString(['minutes', 'seconds', 'secondTenths'])), 
        ]
      }
    >
      <View
        style={[
          styles.hiddenBtn,
          {
            width: 23,
            height: 50,
            left: -15,
            bottom: -15,
            zIndex: 999,
          },
        ]}
      >
        {clicks &&
          clicks.map((item, key) => {
            return (
              item.sentenceKey === sentenceKey &&
              item.wordKey === wordKey && (
                <View
                  key={key}
                  style={{
                    position: "absolute",
                    width: 1,
                    height: 50,
                    left: 8,
                    bottom: 0,
                    borderRightWidth: 2,
                    borderRightStyle: "solid",
                    borderRightColor: "rgb(51, 153, 255)",
                  }}
                ></View>
              )
            );
          })}
      </View>
    </Pressable>
  );
};

export default memo(Clickable);

const styles = StyleSheet.create({
  chain: {
    fontFamily: "Roboto",
    fontFeatureSettings: "tnum",
    fontVariant: 'tabular-nums common-ligatures',
    letterSpacing: 1,
    position: "relative",
  },
  hiddenBtn: {
    //  backgroundColor: "rgba(52, 52, 52, 0.2)",
    position: "absolute",
    // borderWidth: 1,
    // borderStyle: "solid",
    // borderColor: "black",
  },
});
