import { StyleSheet, Text, View } from "react-native";
import React, { useRef, useEffect } from "react";
import Word from "./Word";
import styled from "styled-components/native";
import useTimer from 'easytimer-react-hook';

const Sentence = ({
  sentence,
  sentenceKey,
  onWordPressed,
  clicks,
  index,
  trackedItems,
  timerIsOn,
  lastRemovedItem
}) => {
  const sentenceRef = useRef(null);
  const [easyTimer, isTargetAchieved] = useTimer({});

const startETimer = () => {
   easyTimer.start({precision: 'secondTenths'})
}

useEffect(() => {
  if(trackedItems && trackedItems.length > 0){
    const lastVisible = trackedItems[trackedItems.length-1];
    timerIsOn && sentenceKey === lastVisible.id && lastVisible.visible && startETimer()
    if(clicks.length > 0){
          const lastClick = clicks[clicks.length-1];
          const middeleClick = clicks[clicks.length-2];
          if(middeleClick && lastClick){
          if(sentenceKey === lastVisible.id){
            if(lastClick.sentenceKey !== lastVisible.id && middeleClick.sentenceKey !== lastVisible.id && lastVisible.visible && easyTimer.isRunning()
            && lastRemovedItem && lastRemovedItem.length > 0 && lastRemovedItem[0].wordKey !== lastVisible.id){
              easyTimer.reset()
            }
          } 
        }else if(!middeleClick && lastClick){
          if(sentenceKey === lastVisible.id){
            if(lastClick.sentenceKey !== lastVisible.id && lastVisible.visible && easyTimer.isRunning()){
              easyTimer.reset()
            }
          }
        }
          
          if(trackedItems.length > 1){
            const lastVisibleForScore = trackedItems[trackedItems.length-1];
            sentenceKey !== lastVisible.id && lastClick.sentenceKey === lastVisible.id && lastVisible.visible && easyTimer.isRunning() && easyTimer.pause()
          }
        }
  }
  
}, [trackedItems, timerIsOn, clicks, lastRemovedItem]);



  const pair = (arr) => {
    return arr.arrayOfWords.reduce(
      (acc, cur, i) =>{
         if(arr.correctPosition.some((e, ind) => e === i)){
         return Object.assign([...acc], {
            [acc.length - 1]: [...acc[acc.length - 1], {word: cur, index: i}],
          })
         }
          return [...acc, [{word: cur, index: i}]]
         
           },
      []
    );
  }

  return (
    <SentenceWrap
      style={styles.sentence}
      ref={sentenceRef}
      id="sentence_container"
    >
      {pair(sentence).map((item, indx, array) => {
       return <View key={indx} style={{flexDirection: "row"}}>
          {item.map((y,i) => (
            <Word
              sentence={sentence}
              sentenceKey={sentenceKey}
              word={y.word}
              key={i}
              index={index}
              wordKey={y.index}
              onWordPressed={onWordPressed}
              clicks={clicks}
              element={item}
              easyTimer={easyTimer}
              startETimer={startETimer}
              trackedItems={trackedItems}
            />
          ))}
        </View>;
      })}
    </SentenceWrap>
  );
};

export default Sentence;

const SentenceWrap = styled.Text`
  display: block;
  margin: 0 auto;
  text-align: center;
`;

const styles = StyleSheet.create({
  sentence: {
    // flex: 1,
  },
});
