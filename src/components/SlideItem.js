import {
  StyleSheet,
  Text,
  View,
  Dimensions,
  Animated,
  Easing,
  useWindowDimensions
} from "react-native";
import React, { useEffect, useState } from "react";
import styled from "styled-components/native";
import { useSelector } from "react-redux";



const SlideItem = ({ item }) => {
  const translateYImage = new Animated.Value(40);
  const screenOrientation = useSelector((state) => state.orientation.value);
  const [screenDim, setScreenDim] = useState(Dimensions.get('window'));

  useEffect(() => {
    const updateScreenWidth = () => {
      setScreenDim(Dimensions.get('window'));
    };
  
    Dimensions.addEventListener('change', updateScreenWidth);
  
    // Clean up the event listener when the component unmounts
    return () => {
      Dimensions.removeEventListener('change', updateScreenWidth);
    };
  }, [screenOrientation]);
 

  Animated.timing(translateYImage, {
    toValue: 0,
    duration: 1000,
    useNativeDriver: false,
    easing: Easing.bounce,
  }).start();

  return (
    <Animated.View
      style={[
        styles.container,
        {width: screenDim.width},
        screenOrientation === "landscape" ? {height: '90%'} : {height: screenDim.height} - 270 
      ]}
    >
      <Animated.Image
        source={item.img}
        resizeMode="contain"
        style={[
          styles.image
        ]}
      />
      <View style={styles.content}>
        <Text style={styles.description}>{item.description}</Text>
      </View>
    </Animated.View>
  );
};

export default SlideItem;

const SlideItemWrap = styled.View`
  display: flex;
  width: 900px;
  flex: 1;
  margin: 0 auto;
  text-align: center;
`;

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center"
  },
  image: {
    flex: 0.9,
    width: "90%",
  },
  content: {
    flex: 0.4,
    alignItems: "center",
    width: "70%",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
  },
  description: {
    fontSize: 21,
    marginVertical: 12,
    color: "#333",
    textAlign: "center",
    lineHeight: 25,
  },
});
