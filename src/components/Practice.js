import { StyleSheet, Text, View, Image } from "react-native";
import React, { useRef, useEffect, useState } from "react";
import Word from "./Word";
import styled from "styled-components/native";
import ok from "../../assets/ok.png";
import cancel from "../../assets/cancel.png";
import { useTranslation } from 'react-i18next';

const Practice = ({
  sentence,
  sentenceKey,
  onWordPressed,
  clicks,
  index,
  countCorrectSentences,
  countWrongSentences
}) => {
  const sentenceRef = useRef(null);
  const { t } = useTranslation();




  function pair(arr) {
    return arr.arrayOfWords.reduce(
      (acc, cur, i) =>{
         if(arr.correctPosition.some((e, ind) => e === i)){
         return Object.assign([...acc], {
            [acc.length - 1]: [...acc[acc.length - 1], {word: cur, index: i}],
          })
         }
          return [...acc, [{word: cur, index: i}]]
         
           },
      []
    );
  }

  return (
    <SentenceWrap
      style={styles.sentence}
      ref={sentenceRef}
      id="sentence_container"
    >
    {countCorrectSentences.map((w, i) => {
          return w === sentence.arrayOfWords.join(" ") ? (
            <View
              style={[
                styles.iconContainer,
                {
                  backgroundColor: "#DFF7E8",
                  borderColor: "#177648",
                },
              ]}
              key={i}
            >
              <Text style={{ color: "#177648", fontSize: 14, paddingRight: 5 }}>
              {t('correct')}
              </Text>
              <Image source={ok} style={{ width: 20, height: 20 }} />
            </View>
          ) : null;
        })}
        {countWrongSentences.map((w, i) => {
          return w === sentence.arrayOfWords.join(" ") ? (
            <View
              style={[
                styles.iconContainer,
                {
                  backgroundColor: "#FFF3F3",
                  borderColor: "#F10E0E",
                },
              ]}
              key={i}
            >
              <Text style={{ color: "#D00D0D", fontSize: 14, paddingRight: 5 }}>
              {t('incorrect')}
              </Text>
              <Image source={cancel} style={{ width: 20, height: 20 }} />
            </View>
          ) : null;
        })}
      {pair(sentence).map((item, indx, array) => {
       return <View key={indx} style={{flexDirection: "row"}}>
          {item.map((y,i) => (
            <Word
              sentence={sentence}
              sentenceKey={sentenceKey}
              word={y.word}
              key={i}
              index={index}
              wordKey={y.index}
              onWordPressed={onWordPressed}
              clicks={clicks}
              element={item}
            />
          ))}
        </View>;
      })}
    </SentenceWrap>
  );
};

export default Practice;

const SentenceWrap = styled.Text`
  display: block;
  margin: 0 auto;
  text-align: center;
`;

const styles = StyleSheet.create({
  sentence: {
    // flex: 1,
    position:"relative"
  },
  wrong: {
    fontFamily: "Avenir",
    fontSize: 16,
    color: "red",
  },
  correct: {
    fontFamily: "Avenir",
    fontSize: 16,
    color: "green",
  },
  iconContainer: {
    position: "absolute",
    top: -22,
    start: "40%",
    flexDirection: "row",
    paddingVertical: 4.5,
    paddingHorizontal: 6,
    borderWidth: 1,
    borderRadius: 2,
  },
});
