import { Animated, FlatList, View, Dimensions } from "react-native";
import React, { useRef, useEffect } from "react";
import Slides from "../CarouselData";
import SlideItem from "./SlideItem";
import ArrowNav from "./ArrowNav";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { setScrolledItem, setIndex } from "../store/instructionsSlice";

const Slider = () => {
  const { scrolledItem, index } = useSelector((state) => state.instructions);
  const scrollX = useRef(new Animated.Value(0)).current;
  const listRef = useRef(null);
  const lang = useSelector((state) => state.lang.value);
  const { t } = useTranslation();
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setScrolledItem(0));
  }, []);

  useEffect(() => {
    listRef.current?.scrollToIndex({
      index: scrolledItem,
      animated: true,
      //  viewOffset: scrollX,
      viewPosition: 1,
    });
  }, [scrolledItem]);

  const handleOnScroll = (event) => {
    Animated.event(
      [
        {
          nativeEvent: {
            contentOffset: {
              x: scrollX,
            },
          },
        },
      ],
      {
        useNativeDriver: false,
      }
    )(event);
  };

  const scrollToNext = () => {
    if (scrolledItem < Slides.length - 1) {
      dispatch(setScrolledItem(scrolledItem + 1));
    }
  };
  const scrollToPrevious = () => {
    if (scrolledItem > 0) {
      dispatch(setScrolledItem(scrolledItem - 1));
    }
  };

  const getItemLayout = (data, index) => ({
    length: Dimensions.get("window").width,
    offset: Dimensions.get("window").width * index,
    index,
  });

  const handleOnViewableItemsChanged = useRef(({ viewableItems }) => {
    viewableItems.length > 0 &&
      viewableItems.map((elem) => dispatch(setIndex(elem.index)));
  }).current;

  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50,
  }).current;

  return (
    <View style={{ flex: 1, flexDirection: "row" }}>
      {t("slides", { returnObjects: true }).length > 0 && (
        <FlatList
          ref={listRef}
          data={t("slides", { returnObjects: true })}
          renderItem={({ item }) => <SlideItem item={item} />}
          horizontal
          extraData={lang}
          estimatedItemSize={200}
          snapToAlignment="center"
          showsHorizontalScrollIndicator={false}
          getItemLayout={getItemLayout}
          onScroll={handleOnScroll}
          onViewableItemsChanged={handleOnViewableItemsChanged}
          viewabilityConfig={viewabilityConfig}
          onContentSizeChange={() => {
            if (
              listRef.current &&
              t("slides", { returnObjects: true }) &&
              t("slides", { returnObjects: true }).length
            ) {
              listRef.current.scrollToIndex({ index: 0 });
            }
          }}
        />
      )}
      <ArrowNav
        index={scrolledItem}
        scrollToNext={scrollToNext}
        scrollToPrevious={scrollToPrevious}
      />
    </View>
  );
};

export default Slider;
