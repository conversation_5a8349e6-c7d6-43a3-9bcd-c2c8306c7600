import { StyleSheet, Text, View, Dimensions } from "react-native";
import React from "react";
import { useSelector } from "react-redux";
import { useTranslation } from 'react-i18next';

export default function Header(subTitle, onPressHelp) {
  const { width } = Dimensions.get("window");
  const { t } = useTranslation();
  const lang = useSelector((state) => state.lang.value);
  
  return (
    <View style={{ backgroundColor: "#ffffff", height: 50, justifyContent: "flex-end" }}>
      <View style={{ width: width }}>
      <View style={{flexDirection: "row", alignItems: "center"}}>
        <Text
        lang={lang}
          style={{
            fontWeight: "700",
            fontSize: 22,
            fontFamily: "Avenir",
            color: "#1C1C1C",
            paddingLeft: 30
          }}
        >
          {t('sentences')}
        </Text>
        <Text lang={lang} style={{color: "#808080", fontFamily: "Avenir", fontSize: 16}}> {t(subTitle.subTitle)}</Text>
        </View>
        <View
          style={{
            display: "flex",
            flexDirection: "row",
            width: "100%",
            justifyContent: "space-between",
            paddingHorizontal: 30
          }}
        >
          <View
            style={{
              width: "32%",
              borderRadius: 10,
              height: 5,
              backgroundColor: "#4679BC",
            }}
          ></View>
          <View
            style={{
              width: "32%",
              borderRadius: 10,
              height: 5,
              backgroundColor: "#4679BC",
            }}
          ></View>
          <View
            style={{
              width: "32%",
              borderRadius: 10,
              height: 5,
              backgroundColor: "#4679BC",
            }}
          ></View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({});
