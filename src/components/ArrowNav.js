import {StyleSheet, View, Image, Pressable} from 'react-native';
import React from 'react';
import arrow from '../../assets/arrow.png'



const ArrowNav = ({index, scrollToNext, scrollToPrevious}) => {
  
  
  return (
    <View style={styles.container}>
    {index < 3 && <Pressable hitSlop={30} style={{position: "absolute" ,right: "5%"}} onPress={scrollToNext}>
     <Image 
     source={arrow} 
     style={{width: 50, height: 50, transform: [{scaleX: -1}]}}
     />
     </Pressable>
    }
     {index > 0 && <Pressable hitSlop={20} style={{position: "absolute" ,left: "5%"}} onPress={scrollToPrevious}>
     <Image 
     source={arrow} 
     style={{width: 50, height: 50}}
     />
     </Pressable>}
    </View>
  );
};

export default ArrowNav;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: "10%",
    width: "100%",
    height: 38
  },
});