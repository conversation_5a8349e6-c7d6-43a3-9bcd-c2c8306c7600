import React, { useEffect, useState } from 'react';
import { 
  Modal, 
  View, 
  Text, 
  Pressable, 
  StyleSheet, 
  ActivityIndicator 
} from 'react-native';
import i18n from "../Translations";

const LoadingDots = () => {
  const [dots, setDots] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => {
        if (prev.length >= 3) return '.';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return <Text style={styles.dots}>{dots}</Text>;
};

const ErrorModal = ({ 
  visible, 
  message, 
  onClose, 
  lang, 
  type = 'error',
  isForwarding = false 
}) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <Text 
            lang={lang} 
            style={[
              styles.modalText, 
              type === 'info' ? styles.infoText : styles.errorText
            ]}
          >
            {message}
          </Text>
          
          {isForwarding && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator 
                color="#4679BC" 
                size="large" 
                style={styles.spinner}
              />
              <View style={styles.forwardingTextContainer}>
                <Text style={styles.forwardingText}>
                  {i18n.t('forwardingMessage')}
                </Text>
                <LoadingDots />
              </View>
            </View>
          )}

          {!isForwarding && (
            <Pressable
              style={[
                styles.button,
                type === 'info' ? styles.infoButton : styles.errorButton
              ]}
              onPress={onClose}
            >
              <Text lang={lang} style={styles.buttonText}>OK</Text>
            </Pressable>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '80%',
    maxWidth: 400,
  },
  modalText: {
    textAlign: 'center',
    fontSize: 16,
    lineHeight: 24,
  },
  errorText: {
    color: '#EC3940',
  },
  infoText: {
    color: '#12970b',
  },
  button: {
    borderRadius: 8,
    padding: 12,
    width: '100%',
    alignItems: 'center',
  },
  errorButton: {
    backgroundColor: '#EC3940',
  },
  infoButton: {
    backgroundColor: '#4679BC',
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  spinner: {
    marginBottom: 10,
  },
  forwardingTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  forwardingText: {
    color: '#4679BC',
    fontSize: 16,
    marginRight: 4,
  },
  dots: {
    color: '#4679BC',
    fontSize: 24,
    minWidth: 24,
  },
});

export default ErrorModal;