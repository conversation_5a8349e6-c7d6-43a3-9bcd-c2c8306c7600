import { StyleSheet, Text } from "react-native";
import React, { useEffect, useState, useRef, memo } from "react";
import { RFPercentage } from "react-native-responsive-fontsize";
import Clickable from "./Clickable";
import styled from 'styled-components/native';

const Word = ({
  sentenceKey,
  onWordPressed,
  clicks,
  index,
  wordKey,
  word,
  easyTimer,
  startETimer,
  trackedItems
}) => {
  const wordRef = useRef(null);
 


  return (
    <StyledWord
      style={[
        styles.chain,
        {
          fontSize: 35,
        },
      ]}
      allowFontScaling={false}
      ref={wordRef}
      id="word_container"
    >
    
      <Clickable
        sentenceKey={sentenceKey}
        key={wordKey}
        index={index}
        wordKey={wordKey}
        onWordPressed={onWordPressed}
        clicks={clicks}
        easyTimer={easyTimer}
        startETimer={startETimer}
        trackedItems={trackedItems}
      />
          {`${word} `}
    </StyledWord>

    
  );
};

export default memo(Word);

const StyledWord = styled.Text`
  display: inline-block;
`;

const styles = StyleSheet.create({
  chain: {
    fontFeatureSettings: "tnum",
    fontVariant: 'tabular-nums common-ligatures',
    position: "relative",
  },
  hiddenBtn: {
    position: "absolute",
  },
  spaceElem: {
    flexBasis: "100%",
    height: 0,
  },
});
