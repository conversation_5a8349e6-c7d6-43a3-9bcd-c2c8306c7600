import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  sv: {
    translation:{
    ReadingTest: `L<PERSON>shastighetstest`,
    aboutTest : `Om testet:`,
    infoAboutTest: `Testet utvärderar din läshastighet. Det innehåller tre delar som testar: bokstäver, ord och meningar. Instruktioner ges före början av verje del.`,
    important: `<PERSON><PERSON><PERSON>,
    importantInfo: `Det är viktigt att du gör testet utan avbrott. När testet väl har börjat kommer du inte att kunna pausa eller återuppta testet.`,
    sectionsTitle: `3 delar:`,
    sections: ` bokstäver, ord, meningar`,
    time:`2 minuter`,
    timeInfo: ` per del`,
    repeat:`Upprepa`,
    repeatInfo: `var 6e vecka`,
    toInstruction: `Fortsätt till instruktionerna`,
    enterInfo: `Vänligen registrera din information.`,
    validEmail: `Vänligen ange en giltig e-post`,
    validName: `Vänligen ange en giltig name`,
    validAge: `Vänligen ange en giltig ålder`,
    registerSuccess: `Tack för din registrering!`,
    alreadyReg: `Epost är redan registrerad`,
    firstName: `Förnamn`,
    age: `Ålder`,
    email: `Epost`,
    register: `Registera`,
    sentences: 'Meningar',
    instructions: ` / Instruktioner`,
    extraInstructionsHead: `Instruktioner`,
    practice: `Övningsprov`,
    start: `Starta testet`,
    slides: [
      {
        id: 1,
        img: require('../assets/tut1.png'),
        title: 'Instruction 1',
        description: 'Nya rader med meningar kommer att visas på skärmen.',
      },
      {
        id: 2,
        img: require('../assets/tut2.png'),
        title: 'Instruction 2',
        description: 'Hitta de tre meningarna.',
      },
      {
        id: 3,
        img: require('../assets/tut3.png'),
        title: 'Instruction 3',
        description: 'Tryck mellan meningarna för att dela dem.',
      },
      {
        id: 4,
        img: require('../assets/tut4.png'),
        title: 'Instruction 4',
        description: 'För att rätta tryck på linjen som du vill ändra, och tryck sen på rätt ställe.',
      },
    ],
    ready: `Är du redo?`,
    testTimeInfo: `Du har 2 minuter för att göra testet.`,
    startTest: `Börja`,
    timesUp: `Tiden är ute!`,
    nextSection: `Fortsätt till nästa del.`,
    continue: `Fortsätt`,
    correct: `Rätt`,
    incorrect: `Fel`,
    loading: `Läsa in...`,
    setup: `Ställ in testet`,
    testReady: `Testet är klart`,
    finishTest: `Du klarade lästestet.`,
    showResults: `Visa resultat`,
    goodJob: 'Bra jobbat!',
    results: 'Resultat',
    letters: `Bokstäver`,
    words: `Ord`,
    wait: `Vänligen vänta ...`,
    extraInstructions: [
      'Nya rader med meningar kommer att visas på skärmen.',
      'Hitta de tre meningarna.',
      'Tryck mellan meningarna för att dela dem.',
    ],
    example: "Exempel",
    extraInstructionsImg_1: require('../assets/extraInstra-sc-sv-1.png'),
    extraInstructionsImg_2: require('../assets/extraInstra-sc-sv-2.png'),
    About: "I detta meningstest ska du läsa en rad kombinerade ord. Din uppgift är att identifiera och trycka där en mening slutar och en annan börjar.",
    timeLimitText: "Testet kommer att pågå i två minuter, och din uppgift är att hinna så många meningar som möjligt.",
    timeLimit: "Tidsgräns",
    Note: "Notera",
    NoteText: "För bästa resultat, tysta dina telefonaviseringar och hitta en lugn plats där du kan göra testet ostört. När testet har påbörjats kommer du inte att kunna pausa och återuppta sessionen.",
    CorrectAnswers: `korrekta svar`,
    landscapeText: "Vänligen håll mobilen i liggande läge under nästa del av testet.",
    forwardingMessage: "Går vidare till nästa sida",
    rotateYourPhone: "Snälla! rotera din mobil till liggande läge.", 
    },
  },
  en: {
    translation:{
    ReadingTest: `Reading Test`,
    aboutTest : `About:`,
    infoAboutTest: `The test provides performance measurements into your reading speed. It includes three parts: letters, words, and sentences. Instructions are provided before the start of each section.`,
    important: `Important:`,
    importantInfo: `It’s important you work without interruptions during the test. Once the test begins you will not be able to pause or resume the test.`,
    sectionsTitle: `3 sections:`,
    sections: ` letters, words, sentences`,
    time: `2 minutes`,
    timeInfo: ` each section`,
    repeat:`Repeat`,
    repeatInfo: `every 6 weeks`,
    toInstruction: `Continue to instructions`,
    enterInfo: `Please register your info.`,
    validEmail: `Please enter a valid email`,
    validName: `Please enter a valid name`,
    validAge: `Please enter a valid age`,
    registerSuccess: `Thanks for registration!`,
    alreadyReg: `Email is already registered`,
    firstName: `First name`,
    age: `Age`,
    email: `Email`,
    register: `Register`,
    sentences: `Sentences`,
    instructions: `/ Instructions`,
    extraInstructionsHead: `Instructions`,
    practice: `Practice`,
    start: `Start test`,
    slides:[
      {
        id: 1,
        img: require('../assets/tut1-en.png'),
        title: 'Instruction 1',
        description: 'There will be a new row of combined sentences displayed on the screen.',
      },
      {
        id: 2,
        img: require('../assets/tut2-en.png'),
        title: 'Instruction 2',
        description: 'Find the three sentences.',
      },
      {
        id: 3,
        img: require('../assets/tut3-en.png'),
        title: 'Instruction 3',
        description: 'Tap in between the sentences to split them.',
      },
      {
        id: 4,
        img: require('../assets/tut4-en.png'),
        title: 'Instruction 4',
        description: 'You can correct the first line, but not the second. Focus on the sentences ahead instead.',
      },
    ],
  ready: `Are you ready?`,
  testTimeInfo: `You will have 2 minutes to take the test.`,
  startTest: `I’m ready`,
  timesUp: `Time’s up!`,
  nextSection: `Continue to the next section`,
  continue: `Continue`,
  correct: `Correct`,
  incorrect: `Incorrect`,
  loading: `Loading...`,
  setup: `Setup the test`,
  testReady: `The test is ready`,
  finishTest: `You completed the reading test.`,
  showResults: `See results`,
  goodJob: 'Great job!',
  results: 'Results',
  letters: `Letters`,
  words: `Words`,
  wait: `Please wait ...`,
  extraInstructions: [
    'There will be a new row of combined sentences displayed on the screen.',
    'Find the three sentences.',
    'Tap in between the sentences to split them.',
  ],
  example: "Example",
  extraInstructionsImg_1: require('../assets/extraInstra-sc-en-1.png'),
  extraInstructionsImg_2: require('../assets/extraInstra-sc-en-2.png'),
  About: "The test requires you to read a series of combined sentences. Your task is to identify the points where one sentence ends and another begins.",
  timeLimitText: "The test will last for two minutes, during which you will be challenged with as many sentences as possible.",
  timeLimit: "Time limit",
  Note: "Note",
  NoteText: "For best results, silence your phone notifications, and find a quiet place where you can test undisturbed. Once the test has begun, you will not be able to pause and resume the session.",
  CorrectAnswers: "Correct Answers",
  landscapeText: "Please keep your mobile in landscape mode for the next section of the test.",
  forwardingMessage: "Moving to the next page",
  rotateYourPhone: "Please! rotate your mobile to landscape mode.",
  },
}
};

// const i18n = new I18n(translations)

//  i18n.locale = Localization.locale;

  // i18n.enableFallback = true;

  i18n
  // pass the i18n instance to react-i18next.
  .use(initReactI18next)
  // init i18next
  // for all options read: https://www.i18next.com/overview/configuration-options
  .init({
    debug: true,
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false, // not needed for react as it escapes by default
    },
    resources,
    // lng: "EN"
  });

export default i18n;


