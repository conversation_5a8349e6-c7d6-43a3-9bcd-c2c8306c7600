import {
  doc,
  getDoc,
  updateDoc,
  runTransaction,
} from "firebase/firestore";
import { db } from "../firebaseConfig";
import { LOG_SYSTEM_URL } from "../dir";

export const addTimesToDB = async (timesArr, answers, docId) => {
  const docRef = doc(db, "SentencesChain", docId);
  try {
    const newtimes = await runTransaction(db, async (transaction) => {
      const stSentencesDoc = await transaction.get(docRef);
      if (!stSentencesDoc.exists()) {
        throw "Document does not exist!";
      }

      const transWordsData = stSentencesDoc.data().sentences;
      transWordsData.forEach((item, index) => {
        timesArr &&
          timesArr.map((elm, indx) => {
            if (index === elm.id) {
              if (parseInt(elm.time.slice(0, 2)) < 25) {
                if (answers.isCorrect) {
                  return (item.correctAnswerTimes = [
                    ...item.correctAnswerTimes,
                    elm.time,
                  ]);
                } else {
                  return (item.wrongeAnswerTimes = [
                    ...item.wrongeAnswerTimes,
                    elm.time,
                  ]);
                }
              }
            }
          });
      });

      transaction.set(docRef, { sentences: transWordsData });
    });
  } catch (e) {
    console.error(e);
  }
};

export const addResultsToDB = async (data, correctAnswer, wrongAnswer, userId, testId) => {
  let scoreRes = 0;
  try{
  if(testId){
  const userdocRef = doc(db, "UsersTests", userId, "Tests", testId);
  const docSnap = await getDoc(userdocRef);
  data &&
    data.forEach(async (item, index) => {
      if (correctAnswer) {
          if (item.id === correctAnswer.id) {
            scoreRes = docSnap.data().score + correctAnswer.answerValue;
            const timeValue = parseInt(correctAnswer.time.slice(3, 5)) < 25 ? correctAnswer.time : "25";
                await updateDoc(userdocRef, {
                  ...docSnap.data(),
                  correctSentenceAnswers: [
                    ...docSnap.data().correctSentenceAnswers,
                    { chain: correctAnswer.sentence, time: timeValue, value: correctAnswer.answerValue },
                  ],
                  score: scoreRes
                });
              }
            }
              if(wrongAnswer) {
                if (item.id === wrongAnswer.id) {
                await updateDoc(userdocRef, {
                  ...docSnap.data(),
                  wrongSentenceAnswers: [
                    ...docSnap.data().wrongSentenceAnswers,
                    { chain: wrongAnswer.sentence, time: wrongAnswer.time },
                  ],
                  wrongAnswersScore: docSnap.data().wrongAnswersScore ? docSnap.data().wrongAnswersScore + 1 : 1,
                });
              }
            }
            
          
    });
  }
} catch (e) {
  console.log(e);
}
};

const sendBatchLogs = async (logEntries) => {
    try {
      const formattedLogs = logEntries.map(entry => ({
        key: entry.key || '',
        level: entry.level || 'info',
        message: entry.message || '',
        timestamp: entry.timestamp || new Date().toISOString(),
        meta: entry.meta || {}
      }));
  
      const response = await fetch(`${LOG_SYSTEM_URL}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formattedLogs)
      });
  
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error sending batch logs:', error);
      throw error;
    }
  };

  export const logEvent = async (logText, email, testId, userId) => {
      const logs = [{
        key: email || userId,
        level: 'info',
        message: `${logText} - TestID: ${testId}`,
        timestamp: new Date().toISOString(),
        meta: {
          event: `${logText}`,
          email,
          testId,
          userId,
        }
      }];
      
      return await sendBatchLogs(logs);
    };
